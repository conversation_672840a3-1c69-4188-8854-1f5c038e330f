# DM8 to MySQL 数据库转换说明

## 转换概述

本次转换将 `qData-v1.0.6/docker/database/dm8/init-qdata.sql` (DM8格式) 成功转换为 MySQL 兼容格式。

## 转换结果文件

- **输出文件**: `mysql_final_clean.sql`
- **文件大小**: 15.0 MB
- **总行数**: 213,517 行
- **表数量**: 163 个表
- **数据记录**: 12,160 条 INSERT 语句

## 主要转换规则

### 1. 表名和字段名转换
```sql
-- DM8 格式
CREATE TABLE "QDATA"."SYSTEM_USER"
"USER_ID" BIGINT IDENTITY(2,1) NOT NULL

-- MySQL 格式  
CREATE TABLE `system_user`
`USER_ID` bigint AUTO_INCREMENT NOT NULL
```

### 2. 数据类型转换
| DM8 类型 | MySQL 类型 |
|----------|------------|
| `VARCHAR2(n)` | `varchar(n)` |
| `BIGINT` | `bigint` |
| `INTEGER` | `int` |
| `DATETIME(6)` | `datetime(6)` |
| `TIMESTAMP(0)` | `timestamp` |
| `TEXT` | `text` |
| `CHAR(n)` | `char(n)` |
| `DECIMAL(m,n)` | `decimal(m,n)` |

### 3. 自增字段转换
```sql
-- DM8
"ID" BIGINT IDENTITY(2,1) NOT NULL

-- MySQL
`ID` bigint AUTO_INCREMENT NOT NULL
```

### 4. 日期函数转换
```sql
-- DM8
TO_DATE('2025-09-30 16:24:53','YYYY-MM-DD HH24:MI:SS.FF')

-- MySQL
'2025-09-30 16:24:53'
```

### 5. 表引擎和字符集
所有表都添加了 MySQL 标准配置：
```sql
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

## 使用方法

### 1. 导入到 MySQL 数据库
```bash
# 方法1: 使用 mysql 命令行
mysql -u root -p your_database_name < mysql_final_clean.sql

# 方法2: 使用 Docker 容器
docker exec -i docker-mysql-1 mysql -u root -p'root' your_database_name < mysql_final_clean.sql
```

### 2. 分批导入（推荐大文件）
由于文件较大，建议分批导入：
```bash
# 分割文件
split -l 10000 mysql_final_clean.sql mysql_part_

# 逐个导入
for file in mysql_part_*; do
    mysql -u root -p your_database_name < $file
done
```

## 注意事项

### 1. 数据库准备
在导入前确保：
- MySQL 版本 >= 8.0
- 创建目标数据库
- 确保有足够的存储空间（建议 > 500MB）

### 2. 可能需要调整的项目
- **字段长度**: 某些 VARCHAR 字段长度可能需要根据实际需求调整
- **索引**: 原 DM8 的索引已转换，但可能需要优化
- **约束**: 外键约束可能需要手动检查和调整

### 3. 性能优化建议
```sql
-- 导入前禁用检查（已包含在文件中）
SET FOREIGN_KEY_CHECKS = 0;
SET UNIQUE_CHECKS = 0;
SET AUTOCOMMIT = 0;

-- 导入后重新启用
SET FOREIGN_KEY_CHECKS = 1;
SET UNIQUE_CHECKS = 1;
SET AUTOCOMMIT = 1;
```

## 验证转换结果

### 1. 检查表数量
```sql
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_schema = 'your_database_name';
-- 应该返回 163
```

### 2. 检查核心表结构
```sql
DESCRIBE system_user;
DESCRIBE da_asset;
```

### 3. 检查数据完整性
```sql
-- 检查用户表数据
SELECT COUNT(*) FROM system_user;

-- 检查关键业务表
SELECT COUNT(*) FROM dpp_quality_task;
```

## 转换工具

本次转换使用了自定义 Python 脚本：
- `dm8_to_mysql_final.py` - 主转换脚本
- 支持批量处理大文件
- 自动处理语法差异
- 保持数据完整性

## 支持的功能

✅ **已支持**:
- 表结构转换
- 数据类型映射
- 自增字段转换
- 日期函数转换
- INSERT 语句转换
- 索引和约束转换

❌ **不支持**:
- 存储过程转换
- 触发器转换
- 视图转换（如果有）
- 复杂的 DM8 特有函数

## 故障排除

### 常见错误及解决方案

1. **字符集错误**
   ```sql
   SET NAMES utf8mb4;
   ```

2. **外键约束错误**
   ```sql
   SET FOREIGN_KEY_CHECKS = 0;
   ```

3. **数据过长错误**
   - 检查字段长度定义
   - 必要时调整 VARCHAR 长度

## 联系支持

如果在使用过程中遇到问题，请检查：
1. MySQL 版本兼容性
2. 字符集设置
3. 存储空间是否充足
4. 权限是否正确

转换完成时间: 2025-10-02
转换工具版本: v1.0
