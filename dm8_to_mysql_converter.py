#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DM8 to MySQL SQL Converter
Converts DM8 database schema to MySQL compatible format
"""

import re
import sys
from datetime import datetime

def convert_dm8_to_mysql(input_file, output_file):
    """
    Convert DM8 SQL file to MySQL format
    """
    print(f"Converting {input_file} to {output_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # MySQL header
    mysql_content = f"""-- =====================================================
-- DM8 to MySQL Conversion Script
-- Converted from: {input_file}
-- Target: MySQL 8.0+
-- Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

"""
    
    # Split content into lines for processing
    lines = content.split('\n')
    converted_lines = []
    in_create_table = False
    current_table_name = ""
    table_columns = []
    primary_key_column = ""
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines and comments in original processing
        if not line or line.startswith('--'):
            i += 1
            continue
            
        # Handle CREATE TABLE statements
        if line.startswith('CREATE TABLE "QDATA".'):
            in_create_table = True
            # Extract table name: "QDATA"."TABLE_NAME" -> table_name
            match = re.search(r'"QDATA"\."([^"]+)"', line)
            if match:
                current_table_name = match.group(1).lower()
                converted_lines.append(f"-- Table: {current_table_name}")
                converted_lines.append(f"DROP TABLE IF EXISTS `{current_table_name}`;")
                converted_lines.append(f"CREATE TABLE `{current_table_name}` (")
                table_columns = []
                primary_key_column = ""
            i += 1
            continue
            
        # Handle table closing
        if in_create_table and line == ");":
            # Remove trailing comma from last column if exists
            if converted_lines and converted_lines[-1].strip().endswith(','):
                converted_lines[-1] = converted_lines[-1].rstrip(',')

            # Add primary key if found
            if primary_key_column:
                converted_lines.append(f",")
                converted_lines.append(f"  PRIMARY KEY (`{primary_key_column}`)")

            converted_lines.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;")
            converted_lines.append("")
            in_create_table = False
            current_table_name = ""
            i += 1
            continue
            
        # Handle column definitions within CREATE TABLE
        if in_create_table and line.startswith('"'):
            column_def = convert_column_definition(line)
            if column_def:
                # Check if this is a primary key candidate (ID with IDENTITY)
                if 'AUTO_INCREMENT' in column_def and ('"ID"' in line or '"id"' in line.lower()):
                    col_name_match = re.search(r'"([^"]+)"', line)
                    if col_name_match:
                        primary_key_column = col_name_match.group(1)

                converted_lines.append(f"  {column_def}")
            i += 1
            continue
            
        # Handle INSERT statements
        if line.startswith('INSERT INTO "QDATA".'):
            converted_insert = convert_insert_statement(line)
            if converted_insert:
                converted_lines.append(converted_insert)
            i += 1
            continue
            
        # Handle SET IDENTITY_INSERT statements (skip them)
        if 'SET IDENTITY_INSERT' in line:
            i += 1
            continue
            
        # Handle other statements (indexes, constraints, etc.)
        if line.startswith('CREATE INDEX') or line.startswith('ALTER TABLE') or line.startswith('COMMENT ON'):
            converted_line = convert_other_statements(line)
            if converted_line:
                converted_lines.append(converted_line)
            i += 1
            continue
            
        i += 1
    
    # Add footer
    mysql_content += '\n'.join(converted_lines)
    mysql_content += "\n\nSET FOREIGN_KEY_CHECKS = 1;\n"
    
    # Write to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(mysql_content)
    
    print(f"Conversion completed! Output saved to {output_file}")

def convert_column_definition(line):
    """
    Convert DM8 column definition to MySQL format
    """
    # Remove leading/trailing whitespace and quotes
    line = line.strip()
    if not line or line == 'NULL' or line == 'NOT NULL':
        return None

    # Extract column name
    col_match = re.match(r'"([^"]+)"', line)
    if not col_match:
        return None

    col_name = col_match.group(1)
    rest_of_line = line[col_match.end():].strip()

    # Skip lines that are just NULL or NOT NULL constraints
    if rest_of_line in ['NULL', 'NOT NULL']:
        return None

    # Convert data types
    rest_of_line = convert_data_types(rest_of_line)

    # Handle IDENTITY -> AUTO_INCREMENT
    rest_of_line = re.sub(r'IDENTITY\(\d+,\d+\)', 'AUTO_INCREMENT', rest_of_line)

    # Handle DEFAULT values and NULL constraints
    rest_of_line = fix_default_and_null(rest_of_line)

    # Clean up extra spaces and commas
    rest_of_line = re.sub(r'\s+', ' ', rest_of_line).strip()

    return f"`{col_name}` {rest_of_line}"

def convert_data_types(type_def):
    """
    Convert DM8 data types to MySQL equivalents
    """
    # VARCHAR2 -> varchar
    type_def = re.sub(r'VARCHAR2\((\d+)\)', r'varchar(\1)', type_def)
    
    # BIGINT -> bigint
    type_def = re.sub(r'\bBIGINT\b', 'bigint', type_def)
    
    # INTEGER -> int
    type_def = re.sub(r'\bINTEGER\b', 'int', type_def)
    
    # DATETIME(6) -> datetime(6)
    type_def = re.sub(r'\bDATETIME\((\d+)\)', r'datetime(\1)', type_def)
    
    # TIMESTAMP(0) -> timestamp
    type_def = re.sub(r'\bTIMESTAMP\(0\)', 'timestamp', type_def)
    
    # TEXT -> text
    type_def = re.sub(r'\bTEXT\b', 'text', type_def)
    
    # CHAR -> char
    type_def = re.sub(r'\bCHAR\((\d+)\)', r'char(\1)', type_def)
    
    # DECIMAL -> decimal
    type_def = re.sub(r'\bDECIMAL\((\d+,\d+)\)', r'decimal(\1)', type_def)
    
    return type_def

def fix_default_and_null(col_def):
    """
    Fix DEFAULT values and NULL constraints for MySQL
    """
    # Handle CURRENT_TIMESTAMP
    col_def = re.sub(r'DEFAULT CURRENT_TIMESTAMP', 'DEFAULT CURRENT_TIMESTAMP(6)', col_def)

    # Fix DEFAULT value positioning and NULL constraints
    # Pattern: DEFAULT 'value' NULL -> DEFAULT 'value'
    col_def = re.sub(r"DEFAULT '([^']+)'\s+NULL", r"DEFAULT '\1'", col_def)
    col_def = re.sub(r"DEFAULT (\d+)\s+NULL", r"DEFAULT \1", col_def)
    col_def = re.sub(r"DEFAULT (\d+)\s+NOT NULL", r"NOT NULL DEFAULT \1", col_def)
    col_def = re.sub(r"DEFAULT '([^']+)'\s+NOT NULL", r"NOT NULL DEFAULT '\1'", col_def)

    # Handle standalone NULL/NOT NULL
    if col_def.strip() == 'NULL':
        return 'DEFAULT NULL'
    elif col_def.strip() == 'NOT NULL':
        return 'NOT NULL'

    # Fix NULL positioning
    col_def = re.sub(r'\s+NULL\s*$', '', col_def)
    col_def = re.sub(r'\s+NOT NULL\s*$', ' NOT NULL', col_def)

    # Add ON UPDATE for datetime fields with CURRENT_TIMESTAMP
    if 'datetime' in col_def and 'DEFAULT CURRENT_TIMESTAMP' in col_def and 'UPDATE_TIME' in col_def.upper():
        col_def += ' ON UPDATE CURRENT_TIMESTAMP(6)'

    return col_def.strip()

def convert_insert_statement(line):
    """
    Convert DM8 INSERT statement to MySQL format
    """
    # Convert "QDATA"."TABLE_NAME" to `table_name`
    line = re.sub(r'"QDATA"\."([^"]+)"', lambda m: f"`{m.group(1).lower()}`", line)
    
    # Convert column names in parentheses
    line = re.sub(r'"([^"]+)"', r'`\1`', line)
    
    # Convert TO_DATE functions to MySQL format
    line = re.sub(r"TO_DATE\('([^']+)','[^']+'\)", r"'\1'", line)
    
    return line

def convert_other_statements(line):
    """
    Convert other SQL statements (indexes, constraints, comments)
    """
    # Skip COMMENT ON statements for now
    if line.startswith('COMMENT ON'):
        return None
        
    # Convert table references in other statements
    line = re.sub(r'"QDATA"\."([^"]+)"', lambda m: f"`{m.group(1).lower()}`", line)
    line = re.sub(r'"([^"]+)"', r'`\1`', line)
    
    return line

if __name__ == "__main__":
    input_file = "qData-v1.0.6/docker/database/dm8/init-qdata.sql"
    output_file = "mysql_converted.sql"
    
    try:
        convert_dm8_to_mysql(input_file, output_file)
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found!")
        sys.exit(1)
    except Exception as e:
        print(f"Error during conversion: {e}")
        sys.exit(1)
