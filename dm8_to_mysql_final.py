#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DM8 to MySQL SQL Converter - Final Version
Converts DM8 database schema to MySQL compatible format
"""

import re
import sys
from datetime import datetime

def convert_dm8_to_mysql(input_file, output_file):
    """
    Convert DM8 SQL file to MySQL format
    """
    print(f"Converting {input_file} to {output_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # MySQL header
    mysql_content = f"""-- =====================================================
-- DM8 to MySQL Conversion Script
-- Converted from: {input_file}
-- Target: MySQL 8.0+
-- Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

"""
    
    # Process the content
    converted_content = process_sql_content(content)
    mysql_content += converted_content
    
    # Add footer
    mysql_content += "\n\nSET FOREIGN_KEY_CHECKS = 1;\n"
    
    # Write to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(mysql_content)
    
    print(f"Conversion completed! Output saved to {output_file}")

def process_sql_content(content):
    """
    Process the entire SQL content
    """
    # Split into statements
    statements = re.split(r';\s*\n', content)
    converted_statements = []
    
    for statement in statements:
        statement = statement.strip()
        if not statement:
            continue
            
        if statement.startswith('CREATE TABLE "QDATA".'):
            converted_stmt = convert_create_table(statement)
            if converted_stmt:
                converted_statements.append(converted_stmt)
        elif statement.startswith('INSERT INTO "QDATA".'):
            converted_stmt = convert_insert_statement(statement)
            if converted_stmt:
                converted_statements.append(converted_stmt)
        elif 'SET IDENTITY_INSERT' in statement:
            # Skip IDENTITY_INSERT statements
            continue
        elif statement.startswith('CREATE INDEX') or statement.startswith('ALTER TABLE'):
            converted_stmt = convert_other_statements(statement)
            if converted_stmt:
                converted_statements.append(converted_stmt)
    
    return '\n\n'.join(converted_statements)

def convert_create_table(statement):
    """
    Convert CREATE TABLE statement
    """
    # Extract table name
    table_match = re.search(r'CREATE TABLE "QDATA"\."([^"]+)"', statement)
    if not table_match:
        return None
        
    table_name = table_match.group(1).lower()
    
    # Extract column definitions
    columns_match = re.search(r'\((.*)\)', statement, re.DOTALL)
    if not columns_match:
        return None
        
    columns_text = columns_match.group(1)
    columns = parse_columns(columns_text)
    
    if not columns:
        return None
    
    # Build MySQL CREATE TABLE
    result = f"-- Table: {table_name}\n"
    result += f"DROP TABLE IF EXISTS `{table_name}`;\n"
    result += f"CREATE TABLE `{table_name}` (\n"
    
    column_lines = []
    primary_key_column = None
    
    for col in columns:
        col_line = f"  `{col['name']}` {col['definition']}"
        column_lines.append(col_line)
        
        # Check for primary key
        if col.get('auto_increment') and col['name'].upper() == 'ID':
            primary_key_column = col['name']
    
    result += ',\n'.join(column_lines)
    
    # Add primary key
    if primary_key_column:
        result += f",\n  PRIMARY KEY (`{primary_key_column}`)"
    
    result += "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;"
    
    return result

def parse_columns(columns_text):
    """
    Parse column definitions from CREATE TABLE
    """
    columns = []
    lines = columns_text.split('\n')
    current_column = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Check if this is a new column definition
        if line.startswith('"') and re.match(r'"[^"]+"\s+\w', line):
            # Save previous column
            if current_column:
                columns.append(current_column)
            
            # Start new column
            col_match = re.match(r'"([^"]+)"\s+(.*)', line)
            if col_match:
                col_name = col_match.group(1)
                col_def = col_match.group(2)
                current_column = {
                    'name': col_name,
                    'definition': convert_column_type(col_def),
                    'auto_increment': 'IDENTITY' in col_def
                }
        elif current_column and line in ['NULL', 'NOT NULL']:
            # Handle NULL constraints on separate lines
            if line == 'NOT NULL':
                if 'NOT NULL' not in current_column['definition']:
                    current_column['definition'] += ' NOT NULL'
    
    # Add last column
    if current_column:
        columns.append(current_column)
    
    return columns

def convert_column_type(col_def):
    """
    Convert column type definition
    """
    # Handle IDENTITY
    col_def = re.sub(r'IDENTITY\(\d+,\d+\)', 'AUTO_INCREMENT', col_def)
    
    # Convert data types
    col_def = re.sub(r'VARCHAR2\((\d+)\)', r'varchar(\1)', col_def)
    col_def = re.sub(r'\bBIGINT\b', 'bigint', col_def)
    col_def = re.sub(r'\bINTEGER\b', 'int', col_def)
    col_def = re.sub(r'\bDATETIME\((\d+)\)', r'datetime(\1)', col_def)
    col_def = re.sub(r'\bTIMESTAMP\(0\)', 'timestamp', col_def)
    col_def = re.sub(r'\bTEXT\b', 'text', col_def)
    col_def = re.sub(r'\bCHAR\((\d+)\)', r'char(\1)', col_def)
    col_def = re.sub(r'\bDECIMAL\(([^)]+)\)', r'decimal(\1)', col_def)
    col_def = re.sub(r'\bDECIMAL\b', 'decimal(10,2)', col_def)
    
    # Handle VARCHAR without size
    col_def = re.sub(r'\bVARCHAR\b(?!\()', 'varchar(255)', col_def)
    
    # Fix DEFAULT and NULL positioning
    col_def = re.sub(r"DEFAULT '([^']+)'\s+NULL", r"DEFAULT '\1'", col_def)
    col_def = re.sub(r"DEFAULT (\d+)\s+NULL", r"DEFAULT \1", col_def)
    col_def = re.sub(r"DEFAULT (\d+)\s+NOT NULL", r"NOT NULL DEFAULT \1", col_def)
    
    # Handle CURRENT_TIMESTAMP
    col_def = re.sub(r'DEFAULT CURRENT_TIMESTAMP', 'DEFAULT CURRENT_TIMESTAMP(6)', col_def)
    
    # Add ON UPDATE for update_time columns
    if 'UPDATE_TIME' in col_def.upper() and 'datetime' in col_def and 'DEFAULT CURRENT_TIMESTAMP' in col_def:
        col_def += ' ON UPDATE CURRENT_TIMESTAMP(6)'
    
    # Clean up
    col_def = re.sub(r'\s+', ' ', col_def).strip()
    
    return col_def

def convert_insert_statement(statement):
    """
    Convert INSERT statement
    """
    # Convert table name
    statement = re.sub(r'"QDATA"\."([^"]+)"', lambda m: f"`{m.group(1).lower()}`", statement)
    
    # Convert column names
    statement = re.sub(r'"([^"]+)"', r'`\1`', statement)
    
    # Convert TO_DATE functions
    statement = re.sub(r"TO_DATE\('([^']+)','[^']+'\)", r"'\1'", statement)
    
    return statement

def convert_other_statements(statement):
    """
    Convert other statements
    """
    # Skip COMMENT ON statements
    if statement.startswith('COMMENT ON'):
        return None
    
    # Convert table references
    statement = re.sub(r'"QDATA"\."([^"]+)"', lambda m: f"`{m.group(1).lower()}`", statement)
    statement = re.sub(r'"([^"]+)"', r'`\1`', statement)
    
    return statement

if __name__ == "__main__":
    input_file = "qData-v1.0.6/docker/database/dm8/init-qdata.sql"
    output_file = "mysql_final.sql"
    
    try:
        convert_dm8_to_mysql(input_file, output_file)
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found!")
        sys.exit(1)
    except Exception as e:
        print(f"Error during conversion: {e}")
        sys.exit(1)
