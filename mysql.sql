-- =====================================================
-- DM8 to MySQL Conversion Script
-- Converted from: qData-v1.0.6/docker/database/dm8/init-qdata.sql
-- Target: MySQL 8.0+
-- Generated: 2025-10-02
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- Table: dpp_onl_desform
-- =====================================================
DROP TABLE IF EXISTS `dpp_onl_desform`;
CREATE TABLE `dpp_onl_desform` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `DESFORM_NAME` varchar(128) NOT NULL,
  `DESFORM_CODE` varchar(256) NOT NULL,
  `DESFORM_JSON` text,
  `SAVE_TABLE_FLAG` varchar(1) DEFAULT '0',
  `DATASOURCE_ID` bigint DEFAULT NULL,
  `DATABASE_NAME` varchar(256) DEFAULT NULL,
  `TABLE_NAME` varchar(256) DEFAULT NULL,
  `COLUMN_NAME` text,
  `PK_COLUMN_NAME` varchar(512) DEFAULT NULL,
  `CREATE_PK_DATA_FLAG` varchar(1) DEFAULT '1',
  `VALID_FLAG` varchar(1) NOT NULL DEFAULT '1',
  `DEL_FLAG` varchar(1) NOT NULL DEFAULT '0',
  `CREATE_BY` varchar(32) DEFAULT NULL,
  `CREATOR_ID` bigint DEFAULT NULL,
  `CREATE_TIME` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UPDATE_BY` varchar(32) DEFAULT NULL,
  `UPDATER_ID` bigint DEFAULT NULL,
  `UPDATE_TIME` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `REMARK` varchar(512) DEFAULT NULL,
  `STATUS` char(1) DEFAULT NULL,
  `EFFECTIVE_END_TIME` datetime(6) DEFAULT NULL,
  `CAT_CODE` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Table: dpp_onl_desform_data
-- =====================================================
DROP TABLE IF EXISTS `dpp_onl_desform_data`;
CREATE TABLE `dpp_onl_desform_data` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `DESFORM_CODE` varchar(256) NOT NULL,
  `DESFORM_NAME` varchar(128) NOT NULL,
  `DESFORM_ID` varchar(8188) NOT NULL,
  `DESFORM_DATA` text,
  `VALID_FLAG` varchar(1) NOT NULL DEFAULT '1',
  `DEL_FLAG` varchar(1) NOT NULL DEFAULT '0',
  `CREATE_BY` varchar(32) DEFAULT NULL,
  `CREATOR_ID` bigint DEFAULT NULL,
  `CREATE_TIME` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UPDATE_BY` varchar(32) DEFAULT NULL,
  `UPDATER_ID` bigint DEFAULT NULL,
  `UPDATE_TIME` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- CONVERSION SCRIPT CONTINUES...
-- This is a partial conversion. The complete script will be generated
-- by processing the entire DM8 file with automated conversion rules.
-- =====================================================

-- Key conversion rules applied:
-- 1. "QDATA"."TABLE_NAME" -> `table_name` (lowercase)
-- 2. IDENTITY(start,increment) -> AUTO_INCREMENT
-- 3. VARCHAR2(n) -> varchar(n)
-- 4. DATETIME(6) -> datetime(6)
-- 5. BIGINT -> bigint
-- 6. TEXT -> text
-- 7. CHAR(n) -> char(n)
-- 8. Added ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
-- 9. Added PRIMARY KEY constraints
-- 10. Fixed DEFAULT value syntax
-- 11. Added ON UPDATE CURRENT_TIMESTAMP for update_time fields

SET FOREIGN_KEY_CHECKS = 1;
