# 调度器
HUB=apache
TAG=latest

# 时区
TZ=Asia/Shanghai

# 数据库
DATABASE=postgresql
SPRING_JACKSON_TIME_ZONE=UTC
SPRING_DATASOURCE_URL=*******************************************************************
REGISTRY_ZOOKEEPER_CONNECT_STRING=dolphinscheduler-zookeeper:2181
REGISTRY_ZOOKEEPER_CONNECTION_TIMEOUT=200s
SPRING_DATASOURCE_USERNAME=root
SPRING_DATASOURCE_PASSWORD=root
POSTGRESQL_DATABASE=dolphinscheduler

# datax
DATAX_LAUNCHER=/opt/soft/datax/bin/datax.py
# python
PYTHON_LAUNCHER=/usr/bin/python3
# spark
SPARK_HOME=/opt/soft/spark
#flink
FLINK_HOME=/opt/soft/flink

# rabbitmq
SPRING_RABBITMQ_HOST=rabbitmq
SPRING_RABBITMQ_PORT=5672
SPRING_RABBITMQ_USERNAME=admin
SPRING_RABBITMQ_PASSWORD=Ej^iUNFLp9MQouc1

# ==================================== spark ====================================
# spark 地址
SPARK_MASTER_URL=spark://spark:7077
# spark worker 内存
SPARK_WORKER_MEMORY=5G
# spark worker cpu核数
SPARK_WORKER_CORES=4

# ==================================== qData以及相关中间件 ====================================
# 数据库
CASE_SENSITIVE=0
INSTANCE_NAME=QDATA
# admin 密码
SYSDBA_PWD=o5KjnPYxswsiURZHCPwd
SYSAUDITOR_PWD=o5KjnPYxswsiURZHCPwd
# 表空间名称、密码
QDATA_USER=QDATA
QDATA_PWD=s2LKr6LMQxVDTQx

# redis
REDIS_PASSWORD='J98%FHF#9h@e88h9fre9'
EXPOSE_REDIS_PORT=6379

# rabbitmq
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=Ej^iUNFLp9MQouc1

# nginx
EXPOSE_NGINX_PORT=80
NGINX_PORT=80

# mongodb
MONGO_INITDB_DATABASE=data
MONGO_INITDB_ROOT_USERNAME=sjzt
MONGO_INITDB_ROOT_PASSWORD=Desl9Y4eIQP1BHh7

# ==================================== Hadoop ====================================
CORE-SITE.XML_fs.default.name=hdfs://namenode
CORE-SITE.XML_fs.defaultFS=hdfs://namenode
HDFS-SITE.XML_dfs.namenode.rpc-address=namenode:8020
HDFS-SITE.XML_dfs.replication=1
HDFS-SITE.XML_dfs.datanode.use.datanode.hostname=true
HDFS-SITE.XML_dfs.disk.balancer.enabled=true
HDFS-SITE.XML_dfs.storage.policy.hot.creation.fallbacks=ARCHIVE
HDFS-SITE.XML_dfs.datanode.data.dir=/data/dfs
MAPRED-SITE.XML_mapreduce.framework.name=yarn
MAPRED-SITE.XML_yarn.app.mapreduce.am.env=HADOOP_MAPRED_HOME=$HADOOP_HOME
MAPRED-SITE.XML_mapreduce.map.env=HADOOP_MAPRED_HOME=$HADOOP_HOME
MAPRED-SITE.XML_mapreduce.reduce.env=HADOOP_MAPRED_HOME=$HADOOP_HOME
YARN-SITE.XML_yarn.resourcemanager.hostname=resourcemanager
YARN-SITE.XML_yarn.nodemanager.pmem-check-enabled=false
YARN-SITE.XML_yarn.nodemanager.delete.debug-delay-sec=600
YARN-SITE.XML_yarn.nodemanager.vmem-check-enabled=false
YARN-SITE.XML_yarn.nodemanager.aux-services=mapreduce_shuffle
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.maximum-applications=10000
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.maximum-am-resource-percent=0.1
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.resource-calculator=org.apache.hadoop.yarn.util.resource.DefaultResourceCalculator
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.queues=default
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.default.capacity=100
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.default.user-limit-factor=1
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.default.maximum-capacity=100
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.default.state=RUNNING
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.default.acl_submit_applications=*
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.root.default.acl_administer_queue=*
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.node-locality-delay=40
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.queue-mappings=
CAPACITY-SCHEDULER.XML_yarn.scheduler.capacity.queue-mappings-override.enable=false
