#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import sys
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 配置日志（详细输出转换过程）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DM8ToMySQLConverter:
    def __init__(self):
        # 1. DM8 → MySQL 数据类型映射（覆盖绝大多数场景）
        self.type_mapping: Dict[str, str] = {
            'VARCHAR2': 'VARCHAR',
            'NVARCHAR2': 'VARCHAR',
            'NUMBER': 'DECIMAL',
            'NUMERIC': 'DECIMAL',
            'DEC': 'DECIMAL',
            'INTEGER': 'INT',
            'BIGINT': 'BIGINT',
            'FLOAT': 'DOUBLE',
            'BINARY_FLOAT': 'FLOAT',
            'BINARY_DOUBLE': 'DOUBLE',
            'REAL': 'DOUBLE',
            'DOUBLE': 'DOUBLE',
            'CHAR': 'CHAR',
            'NCHAR': 'CHAR',
            'CLOB': 'LONGTEXT',
            'BLOB': 'LONGBLOB',
            'DATE': 'DATETIME',
            'DATETIME': 'DATETIME',
            'TIMESTAMP': 'DATETIME',
            'TEXT': 'LONGTEXT',
            'LONG': 'LONGTEXT',
            'RAW': 'VARBINARY',
            'BOOLEAN': 'BOOLEAN'
        }

        # 2. 正则表达式模式（覆盖 DM8 特有语法）
        self.patterns: Dict[str, re.Pattern] = {
            # 自增主键：IDENTITY(1,1) → AUTO_INCREMENT
            'identity': re.compile(r'IDENTITY\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)', re.IGNORECASE),
            # 表注释：COMMENT ON TABLE 表名 IS '注释'
            'table_comment': re.compile(r'COMMENT\s+ON\s+TABLE\s+([^\s]+)\s+IS\s+\'([^\']+)\'', re.IGNORECASE),
            # 字段注释：COMMENT ON COLUMN 表名.字段名 IS '注释'
            'column_comment': re.compile(r'COMMENT\s+ON\s+COLUMN\s+([^\.\s]+)\.([^\s]+)\s+IS\s+\'([^\']+)\'', re.IGNORECASE),
            # 序列创建：CREATE SEQUENCE seq_name ...
            'create_sequence': re.compile(r'CREATE\s+SEQUENCE\s+([^\s]+)\s+.*?START\s+WITH\s+(\d+)\s+.*?INCREMENT\s+BY\s+(\d+)', re.IGNORECASE | re.DOTALL),
            # 序列引用：seq_name.NEXTVAL → 自增（仅主键场景）
            'sequence_nextval': re.compile(r'([a-zA-Z_][a-zA-Z0-9_]*\.)?NEXTVAL', re.IGNORECASE),
            # 日期函数：TO_DATE('2023-01-01','YYYY-MM-DD') → '2023-01-01'
            'to_date': re.compile(r'TO_DATE\s*\(\s*\'([^\']+)\'\s*,\s*\'[^\']*\'\s*\)', re.IGNORECASE),
            # 字符串连接：'a'||'b' → CONCAT('a','b')
            'string_concat': re.compile(r'(\'[^\']*\')\s*\|\|\s*(\'[^\']*\')', re.IGNORECASE),
            # NVL 函数：NVL(col,0) → IFNULL(col,0)
            'nvl_function': re.compile(r'NVL\s*\(\s*([^,]+)\s*,\s*([^\)]+)\s*\)', re.IGNORECASE),
            # SYSDATE 函数：SYSDATE → CURRENT_TIMESTAMP
            'sysdate': re.compile(r'\bSYSDATE\b', re.IGNORECASE),
            # TO_CHAR 函数：TO_CHAR(col,'YYYY-MM-DD') → CAST(col AS CHAR)
            'to_char': re.compile(r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'[^\']*\'\s*\)', re.IGNORECASE),
            # TO_NUMBER 函数：TO_NUMBER(col) → CAST(col AS UNSIGNED)
            'to_number': re.compile(r'TO_NUMBER\s*\(\s*([^\)]+)\s*\)', re.IGNORECASE),
            # 双引号处理："表名" → `表名`（MySQL 标识符）
            'double_quotes': re.compile(r'\"([^\"]+)\"'),
            # NUMBER 类型细化：NUMBER(10) → INT，NUMBER(20) → BIGINT
            'numeric_precision': re.compile(r'NUMBER\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)', re.IGNORECASE),
            'numeric_no_scale': re.compile(r'NUMBER\s*\(\s*(\d+)\s*\)', re.IGNORECASE),
            # VARCHAR2 长度：VARCHAR2(50) → VARCHAR(50)
            'varchar2_length': re.compile(r'VARCHAR2\s*\(\s*(\d+)\s*\)', re.IGNORECASE),
            # 日期精度：DATETIME(6) → DATETIME(6)（MySQL 支持）
            'datetime_precision': re.compile(r'DATETIME\s*\(\s*(\d+)\s*\)', re.IGNORECASE),
            # 默认值：DEFAULT SYSDATE → DEFAULT CURRENT_TIMESTAMP
            'default_sysdate': re.compile(r'DEFAULT\s+SYSDATE', re.IGNORECASE),
            # 默认时间戳
            'default_current_timestamp': re.compile(r'DEFAULT\s+CURRENT_TIMESTAMP', re.IGNORECASE),
            # 表名提取
            'table_name': re.compile(r'CREATE\s+TABLE\s+([^\s\(]+)', re.IGNORECASE),
            # 字段定义分割
            'column_definition': re.compile(r'^\s*\"?([^\s\"]+)\"?\s+(.+)$', re.IGNORECASE),
        }

        # 3. 临时存储注释（表→注释，表.字段→注释）
        self.table_comments: Dict[str, str] = {}
        self.column_comments: Dict[str, Dict[str, str]] = {}
        # 4. 临时存储序列（表→序列配置，用于主键自增）
        self.sequences: Dict[str, Dict[str, int]] = {}

    def _escape_sql_string(self, sql: str) -> str:
        """转义SQL字符串中的特殊字符"""
        return sql.replace('\\', '\\\\').replace("'", "\\'").replace('"', '\\"')

    def split_sql_statements(self, sql_content: str) -> List[str]:
        """安全分割 SQL 语句（忽略字符串内的分号）"""
        statements = []
        current_statement = ""
        in_string = False
        string_char = None
        in_comment = False
        comment_type = None  # '--' 或 '/*'
        
        i = 0
        while i < len(sql_content):
            char = sql_content[i]
            
            # 处理注释
            if not in_string and not in_comment:
                if char == '-' and i + 1 < len(sql_content) and sql_content[i+1] == '-':
                    in_comment = True
                    comment_type = '--'
                    current_statement += char
                    i += 1
                elif char == '/' and i + 1 < len(sql_content) and sql_content[i+1] == '*':
                    in_comment = True
                    comment_type = '/*'
                    current_statement += char
                    i += 1
                elif char == "'" or char == '"':
                    in_string = True
                    string_char = char
                    current_statement += char
                elif char == ';':
                    current_statement += char
                    statements.append(current_statement.strip())
                    current_statement = ""
                else:
                    current_statement += char
            
            # 在字符串中
            elif in_string:
                current_statement += char
                if char == string_char:
                    # 检查是否转义
                    if i > 0 and sql_content[i-1] == '\\':
                        pass  # 转义的引号，继续
                    else:
                        in_string = False
                        string_char = None
            
            # 在注释中
            elif in_comment:
                current_statement += char
                if comment_type == '--' and char == '\n':
                    in_comment = False
                    comment_type = None
                elif comment_type == '/*' and char == '*' and i + 1 < len(sql_content) and sql_content[i+1] == '/':
                    current_statement += sql_content[i+1]  # 添加 '/'
                    i += 1
                    in_comment = False
                    comment_type = None
            
            i += 1
        
        # 添加最后一个语句（如果没有分号结尾）
        if current_statement.strip():
            statements.append(current_statement.strip())
        
        return statements

    def convert_data_type(self, column_def: str) -> str:
        """转换 DM8 数据类型为 MySQL 类型"""
        original_def = column_def.upper()
        converted_def = column_def

        # 1. 处理自增：IDENTITY(1,1) → AUTO_INCREMENT
        identity_match = self.patterns['identity'].search(column_def)
        if identity_match:
            converted_def = self.patterns['identity'].sub('AUTO_INCREMENT', converted_def)

        # 2. 处理 NUMBER 类型细化
        if 'NUMBER' in original_def:
            precision_match = self.patterns['numeric_precision'].search(column_def)
            no_scale_match = self.patterns['numeric_no_scale'].search(column_def)

            if precision_match:
                precision = int(precision_match.group(1))
                scale = int(precision_match.group(2))
                if scale == 0:  # 无小数位
                    if precision <= 10:
                        converted_def = converted_def.replace(precision_match.group(0), 'INT')
                    elif precision <= 19:
                        converted_def = converted_def.replace(precision_match.group(0), 'BIGINT')
                    else:
                        converted_def = converted_def.replace(precision_match.group(0), f'DECIMAL({precision},0)')
                else:  # 有小数位
                    converted_def = converted_def.replace(precision_match.group(0), f'DECIMAL({precision},{scale})')

            elif no_scale_match:
                precision = int(no_scale_match.group(1))
                if precision <= 10:
                    converted_def = converted_def.replace(no_scale_match.group(0), 'INT')
                elif precision <= 19:
                    converted_def = converted_def.replace(no_scale_match.group(0), 'BIGINT')
                else:
                    converted_def = converted_def.replace(no_scale_match.group(0), f'DECIMAL({precision},0)')
            else:
                # 没有精度的 NUMBER
                converted_def = converted_def.replace('NUMBER', 'DECIMAL(65,30)')

        # 3. 处理 VARCHAR2 类型
        elif 'VARCHAR2' in original_def:
            match = self.patterns['varchar2_length'].search(column_def)
            if match:
                length = match.group(1)
                converted_def = converted_def.replace(match.group(0), f'VARCHAR({length})')

        # 4. 通用类型映射
        for dm_type, mysql_type in self.type_mapping.items():
            pattern = re.compile(r'\b' + re.escape(dm_type) + r'\b', re.IGNORECASE)
            if pattern.search(converted_def):
                converted_def = pattern.sub(mysql_type, converted_def)

        # 5. 处理默认值：DEFAULT SYSDATE → DEFAULT CURRENT_TIMESTAMP
        if self.patterns['default_sysdate'].search(converted_def):
            converted_def = self.patterns['default_sysdate'].sub('DEFAULT CURRENT_TIMESTAMP', converted_def)

        return converted_def

    def extract_column_name_and_definition(self, column_line: str) -> Tuple[str, str]:
        """提取字段名和字段定义"""
        match = self.patterns['column_definition'].match(column_line)
        if match:
            return match.group(1), match.group(2)
        return "", column_line

    def convert_column_definition(self, table_name: str, column_line: str) -> str:
        """转换字段定义"""
        # 移除双引号
        column_line = self.patterns['double_quotes'].sub(r'`\1`', column_line)
        
        # 提取字段名和定义
        col_name, col_def = self.extract_column_name_and_definition(column_line)
        if not col_name:
            return column_line

        # 转换数据类型
        converted_def = self.convert_data_type(col_def)
        
        # 构建完整的字段定义
        result = f"  `{col_name}` {converted_def}"
        
        # 确保 NOT NULL/NULL 正确
        if 'NOT NULL' in converted_def.upper():
            # 如果已经存在 NOT NULL，确保格式正确
            pass
        elif 'NULL' in converted_def.upper() and 'NOT NULL' not in converted_def.upper():
            pass
        else:
            # 根据原始定义添加
            if 'NOT NULL' in col_def.upper():
                result += ' NOT NULL'
            elif 'NULL' in col_def.upper():
                result += ' NULL'

        # 处理默认值（需要更精确的提取）
        default_match = re.search(r'DEFAULT\s+([^,\s]+)', col_def, re.IGNORECASE)
        if default_match and 'DEFAULT' not in result.upper():
            default_value = default_match.group(1)
            if default_value.upper() in ['CURRENT_TIMESTAMP', 'SYSDATE']:
                result += ' DEFAULT CURRENT_TIMESTAMP'
            elif default_value.replace("'", "").replace('"', '').replace('.', '').isdigit():
                result += f' DEFAULT {default_value}'
            else:
                # 字符串默认值
                clean_value = default_value.strip("'").strip('"')
                result += f" DEFAULT '{self._escape_sql_string(clean_value)}'"

        # 添加字段注释
        if table_name in self.column_comments and col_name in self.column_comments[table_name]:
            comment = self.column_comments[table_name][col_name]
            if 'COMMENT' not in result.upper():
                result += f" COMMENT '{self._escape_sql_string(comment)}'"

        return result

    def convert_table_definition(self, table_sql: str) -> str:
        """转换 CREATE TABLE 语句"""
        # 提取表名
        table_name_match = self.patterns['table_name'].search(table_sql)
        if not table_name_match:
            logger.warning(f"无法提取表名: {table_sql[:100]}...")
            return f"/* 转换失败：无法提取表名 */\n{table_sql}"

        original_table_name = table_name_match.group(1)
        table_name = self.patterns['double_quotes'].sub(r'\1', original_table_name)
        
        # 替换双引号为 MySQL 反引号
        converted_sql = self.patterns['double_quotes'].sub(r'`\1`', table_sql)

        # 提取表定义部分
        start_pos = converted_sql.find('(')
        end_pos = converted_sql.rfind(')')
        
        if start_pos == -1 or end_pos == -1:
            logger.warning(f"表结构格式异常: {table_sql[:100]}...")
            return f"/* 转换失败：表结构格式异常 */\n{table_sql}"

        table_header = converted_sql[:start_pos].replace('CREATE TABLE', 'CREATE TABLE IF NOT EXISTS')
        table_body = converted_sql[start_pos + 1:end_pos].strip()
        
        # 分割字段定义
        lines = []
        current_line = ""
        paren_depth = 0
        
        for char in table_body + ',':
            if char == '(':
                paren_depth += 1
                current_line += char
            elif char == ')':
                paren_depth -= 1
                current_line += char
            elif char == ',' and paren_depth == 0:
                if current_line.strip():
                    lines.append(current_line.strip())
                current_line = ""
            else:
                current_line += char

        # 转换每个字段
        converted_lines = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 处理约束（PRIMARY KEY, FOREIGN KEY 等）
            if any(keyword in line.upper() for keyword in 
                  ['PRIMARY KEY', 'FOREIGN KEY', 'UNIQUE', 'CONSTRAINT', 'CHECK']):
                # 简单处理约束，移除双引号
                constraint_line = self.patterns['double_quotes'].sub(r'`\1`', line)
                converted_lines.append(f"  {constraint_line}")
            else:
                # 转换字段定义
                converted_col = self.convert_column_definition(table_name, line)
                converted_lines.append(converted_col)

        # 构建最终表定义
        table_comment = self.table_comments.get(table_name.upper(), '')
        
        result = f"{table_header} (\n"
        result += ",\n".join(converted_lines)
        result += "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        
        if table_comment:
            result += f" COMMENT='{self._escape_sql_string(table_comment)}'"
        
        result += ";"
        
        logger.info(f"成功转换表: {table_name}")
        return result

    def convert_insert_statement(self, insert_sql: str) -> str:
        """转换 INSERT 语句"""
        converted_sql = insert_sql

        # 替换双引号
        converted_sql = self.patterns['double_quotes'].sub(r'`\1`', converted_sql)

        # 转换函数
        conversions = [
            (self.patterns['to_date'], r"'\1'"),
            (self.patterns['nvl_function'], r"IFNULL(\1, \2)"),
            (self.patterns['sysdate'], "CURRENT_TIMESTAMP"),
            (self.patterns['to_char'], r"CAST(\1 AS CHAR)"),
            (self.patterns['to_number'], r"CAST(\1 AS UNSIGNED)"),
        ]

        for pattern, replacement in conversions:
            converted_sql = pattern.sub(replacement, converted_sql)

        # 处理字符串连接（需要递归处理）
        while self.patterns['string_concat'].search(converted_sql):
            converted_sql = self.patterns['string_concat'].sub(r"CONCAT(\1, \2)", converted_sql)

        return converted_sql

    def process_sequence_statement(self, seq_sql: str) -> str:
        """处理 CREATE SEQUENCE 语句"""
        match = self.patterns['create_sequence'].search(seq_sql)
        if not match:
            return f"/* 无法解析序列: {seq_sql} */"

        seq_name = match.group(1)
        start_with = match.group(2)
        
        hint = f"""/* 
序列 {seq_name} 转换提示：
DM8 序列需要手动转换，建议方案：
1. 使用 AUTO_INCREMENT 替代（如果用于主键）
2. 创建序列表模拟序列行为
示例序列表：
CREATE TABLE `{seq_name}_seq` (
    `next_val` BIGINT NOT NULL
);
INSERT INTO `{seq_name}_seq` VALUES ({start_with});
*/"""
        return hint

    def process_comment_statement(self, comment_sql: str) -> None:
        """处理 COMMENT ON 语句"""
        # 表注释
        table_match = self.patterns['table_comment'].search(comment_sql)
        if table_match:
            table_name = self.patterns['double_quotes'].sub(r'\1', table_match.group(1))
            comment = table_match.group(2)
            self.table_comments[table_name.upper()] = comment
            return

        # 字段注释
        column_match = self.patterns['column_comment'].search(comment_sql)
        if column_match:
            table_name = self.patterns['double_quotes'].sub(r'\1', column_match.group(1))
            col_name = self.patterns['double_quotes'].sub(r'\1', column_match.group(2))
            comment = column_match.group(3)
            
            if table_name.upper() not in self.column_comments:
                self.column_comments[table_name.upper()] = {}
            self.column_comments[table_name.upper()][col_name] = comment

    def process_sql_statement(self, sql: str) -> str:
        """处理单个 SQL 语句"""
        sql_upper = sql.upper().strip()

        try:
            if sql_upper.startswith('COMMENT ON'):
                self.process_comment_statement(sql)
                return ""  # 注释语句不直接输出

            elif sql_upper.startswith('CREATE TABLE'):
                return self.convert_table_definition(sql)

            elif sql_upper.startswith('INSERT INTO'):
                return self.convert_insert_statement(sql)

            elif sql_upper.startswith('CREATE SEQUENCE'):
                return self.process_sequence_statement(sql)

            elif any(sql_upper.startswith(keyword) for keyword in 
                   ['CREATE VIEW', 'CREATE PROCEDURE', 'CREATE FUNCTION', 'CREATE TRIGGER']):
                obj_type = {
                    'CREATE VIEW': '视图',
                    'CREATE PROCEDURE': '存储过程', 
                    'CREATE FUNCTION': '函数',
                    'CREATE TRIGGER': '触发器'
                }
                for key, value in obj_type.items():
                    if sql_upper.startswith(key):
                        return f"/* 需手动转换{value}:\n{sql}\n*/"

            else:
                # 其他语句简单处理双引号
                converted = self.patterns['double_quotes'].sub(r'`\1`', sql)
                return f"/* 需手动检查语句:\n{converted}\n*/"

        except Exception as e:
            logger.error(f"处理SQL语句时出错: {e}")
            return f"/* 转换出错: {e}\n原始SQL:\n{sql}\n*/"

        return ""

    def process_large_file(self, input_file: str, output_file: str, buffer_size: int = 8192 * 1024) -> None:
        """处理大文件转换"""
        logger.info(f"开始转换: {input_file} -> {output_file}")

        # 初始化输出文件
        with open(output_file, 'w', encoding='utf-8') as out_f:
            out_f.write("-- DM8 转 MySQL 自动转换脚本\n")
            out_f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            out_f.write("-- 注意：复杂对象（存储过程/触发器）需手动转换\n\n")
            out_f.write("SET NAMES utf8mb4;\n")
            out_f.write("SET FOREIGN_KEY_CHECKS=0;\n")
            out_f.write("SET AUTOCOMMIT=0;\n\n")

        # 处理文件内容
        total_statements = 0
        converted_statements = 0
        
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as in_f:
            # 读取整个文件内容（对于大文件可能需要分块，但分割逻辑已处理）
            content = in_f.read()
            
        # 分割SQL语句
        statements = self.split_sql_statements(content)
        total_statements = len(statements)
        
        logger.info(f"共识别 {total_statements} 条SQL语句")
        
        # 处理每条语句
        with open(output_file, 'a', encoding='utf-8') as out_f:
            for i, stmt in enumerate(statements):
                if not stmt.strip():
                    continue
                    
                try:
                    converted = self.process_sql_statement(stmt)
                    if converted:
                        out_f.write(converted + "\n\n")
                        converted_statements += 1
                        
                    if (i + 1) % 100 == 0:
                        logger.info(f"处理进度: {i+1}/{total_statements}")
                        
                except Exception as e:
                    logger.error(f"处理第 {i+1} 条语句时出错: {e}")
                    out_f.write(f"/* 处理出错: {e} */\n")
                    out_f.write(f"/* 原始语句: {stmt} */\n\n")

        # 写入结束语句
        with open(output_file, 'a', encoding='utf-8') as out_f:
            out_f.write("\nCOMMIT;\n")
            out_f.write("SET FOREIGN_KEY_CHECKS=1;\n")
            out_f.write("SET AUTOCOMMIT=1;\n")
            out_f.write(f"-- 转换完成！共处理 {converted_statements} 条语句\n")

        logger.info(f"转换完成: 输出文件 {output_file}")
        logger.info(f"统计: 总语句 {total_statements}, 成功转换 {converted_statements}")

def main() -> None:
    if len(sys.argv) != 3:
        print("用法: python dm8_to_mysql.py <DM8输入SQL文件> <MySQL输出SQL文件>")
        print("示例: python dm8_to_mysql.py dm8_dump.sql mysql_import.sql")
        sys.exit(1)

    input_path = Path(sys.argv[1])
    output_path = Path(sys.argv[2])

    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_path}")
        sys.exit(1)

    if output_path.exists():
        confirm = input(f"输出文件 {output_path} 已存在，是否覆盖？(y/N): ").strip().lower()
        if confirm != 'y':
            logger.info("用户取消操作")
            sys.exit(0)

    converter = DM8ToMySQLConverter()
    try:
        converter.process_large_file(str(input_path), str(output_path))
        logger.info("转换完成！")
    except Exception as e:
        logger.error(f"转换过程出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()