CREATE TABLE "WATER_TP"."DISCHARGE"
(
 "ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "OBS_TIME" TIMESTAMP(0) NOT NULL,
 "<PERSON><PERSON><PERSON>AR<PERSON>_CMS" DECIMAL(12,3) NOT NULL,
 "QUALITY_FLAG" VARCHAR2(16) NULL,
 "TS" TIMESTAMP(0) NULL
);
CREATE TABLE "WATER_TP"."RAINFALL"
(
 "ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "OBS_TIME" TIMESTAMP(0) NOT NULL,
 "PRECIP_MM" DECIMAL(10,2) NOT NULL,
 "QUALITY_FLAG" VARCHAR2(16) NULL,
 "TS" TIMESTAMP(0) NULL
);
CREATE TABLE "WATER_TP"."RESERVOIR_LEVEL"
(
 "ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "OBS_TIME" TIMESTAMP(0) NOT NULL,
 "WATER_LEVEL" DECIMAL(10,3) NOT NULL,
 "STORAGE_MCM" DECIMAL(14,3) NULL,
 "OUTFLOW_CMS" DECIMAL(12,3) NULL,
 "INFLOW_CMS" DECIMAL(12,3) NULL,
 "QUALITY_FLAG" VARCHAR2(16) NULL,
 "TS" TIMESTAMP(0) NULL
);
CREATE TABLE "WATER_TP"."STATION"
(
 "STATION_ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "STATION_NAME" VARCHAR2(200) NOT NULL,
 "BASIN_CODE" VARCHAR2(64) NULL,
 "RIVER_NAME" VARCHAR2(128) NULL,
 "LONGITUDE" DECIMAL(10,6) NULL,
 "LATITUDE" DECIMAL(10,6) NULL,
 "ADMIN_REGION_CODE" VARCHAR2(12) NULL,
 "STATUS" NUMBER(1,0) DEFAULT 1
 NOT NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);
CREATE TABLE "WATER_TP"."STATION_ATTR"
(
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "ATTR_KEY" VARCHAR2(64) NOT NULL,
 "ATTR_VALUE" VARCHAR2(4000) NULL,
 "VALUE_TYPE" VARCHAR2(16) DEFAULT 'STRING'
 NOT NULL,
 "REMARK" VARCHAR2(500) NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);
CREATE TABLE "WATER_TP"."STATION_THRESHOLD"
(
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "EFFECTIVE_FROM" TIMESTAMP(0) NOT NULL,
 "EFFECTIVE_TO" TIMESTAMP(0) NULL,
 "LEVEL_BLUE" DECIMAL(10,3) NULL,
 "LEVEL_YELLOW" DECIMAL(10,3) NULL,
 "LEVEL_RED" DECIMAL(10,3) NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);
CREATE TABLE "WATER_TP"."WARNING_EVENT"
(
 "EVENT_ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "START_TIME" TIMESTAMP(0) NOT NULL,
 "END_TIME" TIMESTAMP(0) NULL,
 "WARNING_LEVEL" NUMBER(1,0) NOT NULL,
 "MAX_LEVEL" DECIMAL(10,3) NULL,
 "STATUS" VARCHAR2(16) DEFAULT 'active'
 NOT NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);
CREATE TABLE "WATER_TP"."WATER_LEVEL"
(
 "ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "OBS_TIME" TIMESTAMP(0) NOT NULL,
 "WATER_LEVEL" VARCHAR2(64) NOT NULL,
 "QUALITY_FLAG" VARCHAR2(16) NULL,
 "TS" TIMESTAMP(0) NULL
);
CREATE TABLE "WATER_TP"."DEVICE"
(
 "DEVICE_ID" NUMBER(19,0) NOT NULL,
 "STATION_CODE" VARCHAR2(64) NOT NULL,
 "DEVICE_TYPE" VARCHAR2(64) NOT NULL,
 "DEVICE_MODEL" VARCHAR2(128) NULL,
 "SERIAL_NO" VARCHAR2(128) NULL,
 "INSTALL_TIME" TIMESTAMP(0) NULL,
 "STATUS" NUMBER(1,0) DEFAULT 1
 NOT NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);
CREATE TABLE "WATER_TP"."ADMIN_REGION_DICT"
(
 "REGION_CODE" VARCHAR2(12) NOT NULL,
 "REGION_NAME" VARCHAR2(200) NOT NULL,
 "PARENT_CODE" VARCHAR2(12) NULL,
 "LEVEL_NO" NUMBER(2,0) DEFAULT 1
 NOT NULL,
 "STATUS" NUMBER(1,0) DEFAULT 1
 NOT NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);
CREATE TABLE "WATER_TP"."BASIN_DICT"
(
 "BASIN_CODE" VARCHAR2(64) NOT NULL,
 "BASIN_NAME" VARCHAR2(200) NOT NULL,
 "PARENT_CODE" VARCHAR2(64) NULL,
 "LEVEL_NO" NUMBER(2,0) DEFAULT 1
 NOT NULL,
 "STATUS" NUMBER(1,0) DEFAULT 1
 NOT NULL,
 "CREATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL,
 "UPDATED_AT" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP
 NOT NULL
);

-- 如存在则先删除
DROP TABLE IF EXISTS "WATER_TP"."STATION_WATER_MONTH_REPORT";

-- 建表
CREATE TABLE "WATER_TP"."STATION_WATER_MONTH_REPORT" (
                                                         "STATION_ID"      NUMBER(19,0)   NOT NULL,
                                                         "STATION_NAME"    VARCHAR2(200)  NOT NULL,
                                                         "AVG_WATER_LEVEL" VARCHAR2(200),
                                                         "STAT_MONTH"      VARCHAR2(7)    NOT NULL
);

-- 表注释
COMMENT ON TABLE "WATER_TP"."STATION_WATER_MONTH_REPORT" IS '站点水位月报表';

-- 字段注释
COMMENT ON COLUMN "WATER_TP"."STATION_WATER_MONTH_REPORT"."STATION_ID" IS '测站ID';
COMMENT ON COLUMN "WATER_TP"."STATION_WATER_MONTH_REPORT"."STATION_NAME" IS '测站名称';
COMMENT ON COLUMN "WATER_TP"."STATION_WATER_MONTH_REPORT"."AVG_WATER_LEVEL" IS '月平均水位';
COMMENT ON COLUMN "WATER_TP"."STATION_WATER_MONTH_REPORT"."STAT_MONTH" IS '统计时间（YYYY-MM，例如：2025-01）';

INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(1,'ST0001','黄河兰州站','HN','黄河',103.834560,36.061380,'620100',1,TO_DATE('2025-09-18 16:31:29','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:31:29','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(2,'ST0002','长江宜昌站',null,null,111.286500,30.691900,'420500',1,TO_DATE('2025-09-18 16:31:29','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:31:29','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(3,'ST0003','珠江广州站','PR','珠江',113.264400,23.129100,'440100',0,TO_DATE('2025-09-18 16:31:29','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:31:29','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(4,'ST0004','松花江哈尔滨站','SH','松花江',126.642400,45.756900,'230100',1,TO_DATE('2025-01-01 08:00:00','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-01-01 08:00:00','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(5,'ST0005','站点5','CH','长江',119.407852,35.146358,'CN0001',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(6,'ST0006','站点6','PR','珠江',101.827486,30.917226,'CN0002',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(7,'ST0007','站点7','HN','黄河',104.903261,38.507963,'CN0003',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(8,'ST0008','站点8','CH','长江',109.316993,38.722702,'CN0004',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(9,'ST0009','站点9','PR','珠江',114.807481,30.141796,'CN0005',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(10,'ST0010','站点10','HN','黄河',100.699556,30.532644,'CN0006',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(11,'ST0011','站点11','CH','长江',119.328794,34.160080,'CN0007',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(12,'ST0012','站点12','PR','珠江',107.680790,30.827245,'CN0008',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(13,'ST0013','站点13','HN','黄河',104.288235,35.938542,'CN0009',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(14,'ST0014','站点14','CH','长江',113.081231,39.631883,'CN0010',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(15,'ST0015','站点15','PR','珠江',118.383921,30.118295,'CN0011',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(16,'ST0016','站点16','HN','黄河',100.851882,34.457900,'CN0012',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(17,'ST0017','站点17','CH','长江',116.150212,30.796175,'CN0013',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(18,'ST0018','站点18','PR','珠江',112.198147,30.429372,'CN0014',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(19,'ST0019','站点19','HN','黄河',114.145906,30.844538,'CN0015',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(20,'ST0020','站点20','CH','长江',106.464565,31.351951,'CN0016',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(21,'ST0021','站点21','PR','珠江',119.032775,30.270912,'CN0017',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(22,'ST0022','站点22','HN','黄河',109.128796,37.926833,'CN0018',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(23,'ST0023','站点23','CH','长江',116.206096,39.779154,'CN0019',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(24,'ST0024','站点24','PR','珠江',100.269955,38.917746,'CN0020',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(25,'ST0025','站点25','HN','黄河',106.922704,32.580401,'CN0021',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(26,'ST0026','站点26','CH','长江',108.279948,33.960682,'CN0022',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(27,'ST0027','站点27','PR','珠江',112.411989,37.533886,'CN0023',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(28,'ST0028','站点28','HN','黄河',102.505924,31.390252,'CN0024',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(29,'ST0029','站点29','CH','长江',113.786468,30.040607,'CN0025',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(30,'ST0030','站点30','PR','珠江',111.138682,33.374756,'CN0026',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(31,'ST0031','站点31','HN','黄河',106.788669,34.089221,'CN0027',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(32,'ST0032','站点32','CH','长江',107.249768,32.642130,'CN0028',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(33,'ST0033','站点33','PR','珠江',117.180251,32.441791,'CN0029',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(34,'ST0034','站点34','HN','黄河',105.745498,37.885084,'CN0030',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(35,'ST0035','站点35','CH','长江',104.120170,30.282061,'CN0031',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(36,'ST0036','站点36','PR','珠江',100.528142,37.706280,'CN0032',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(37,'ST0037','站点37','HN','黄河',119.976530,36.521607,'CN0033',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(38,'ST0038','站点38','CH','长江',116.774359,38.760051,'CN0034',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(39,'ST0039','站点39','PR','珠江',112.019051,32.316941,'CN0035',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(40,'ST0040','站点40','HN','黄河',104.462708,32.028739,'CN0036',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(41,'ST0041','站点41','CH','长江',117.256157,36.796856,'CN0037',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(42,'ST0042','站点42','PR','珠江',101.671943,30.308447,'CN0038',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(43,'ST0043','站点43','HN','黄河',110.785052,39.293749,'CN0039',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(44,'ST0044','站点44','CH','长江',119.358829,39.746159,'CN0040',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(45,'ST0045','站点45','PR','珠江',101.450039,36.931433,'CN0041',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(46,'ST0046','站点46','HN','黄河',101.999781,32.349538,'CN0042',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(47,'ST0047','站点47','CH','长江',118.951422,37.817235,'CN0043',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(48,'ST0048','站点48','PR','珠江',113.398593,34.870027,'CN0044',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(49,'ST0049','站点49','HN','黄河',112.974697,37.546558,'CN0045',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(50,'ST0050','站点50','CH','长江',115.939367,31.333425,'CN0046',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(51,'ST0051','站点51','PR','珠江',118.140103,39.638275,'CN0047',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(52,'ST0052','站点52','HN','黄河',110.478298,32.160141,'CN0048',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(53,'ST0053','站点53','CH','长江',105.891108,35.920344,'CN0049',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(54,'ST0054','站点54','PR','珠江',107.861710,36.586619,'CN0050',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(55,'ST0055','站点55','HN','黄河',114.694675,37.974212,'CN0051',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(56,'ST0056','站点56','CH','长江',110.734481,37.682780,'CN0052',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(57,'ST0057','站点57','PR','珠江',108.526418,35.377330,'CN0053',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(58,'ST0058','站点58','HN','黄河',113.929260,30.276871,'CN0054',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(59,'ST0059','站点59','CH','长江',107.532559,35.665279,'CN0055',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(60,'ST0060','站点60','PR','珠江',105.431469,32.574369,'CN0056',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(61,'ST0061','站点61','HN','黄河',117.533237,37.334571,'CN0057',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(62,'ST0062','站点62','CH','长江',118.889468,37.122966,'CN0058',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(63,'ST0063','站点63','PR','珠江',101.755685,33.646446,'CN0059',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(64,'ST0064','站点64','HN','黄河',118.137314,37.369350,'CN0060',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(65,'ST0065','站点65','CH','长江',110.521433,36.949422,'CN0061',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(66,'ST0066','站点66','PR','珠江',108.535719,32.619310,'CN0062',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(67,'ST0067','站点67','HN','黄河',115.781458,31.348735,'CN0063',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(68,'ST0068','站点68','CH','长江',117.493919,38.401347,'CN0064',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(69,'ST0069','站点69','PR','珠江',117.182443,34.105168,'CN0065',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(70,'ST0070','站点70','HN','黄河',104.752745,36.601792,'CN0066',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(71,'ST0071','站点71','CH','长江',116.103517,31.591376,'CN0067',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(72,'ST0072','站点72','PR','珠江',103.391029,32.249619,'CN0068',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(73,'ST0073','站点73','HN','黄河',111.736311,35.074144,'CN0069',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(74,'ST0074','站点74','CH','长江',105.630020,32.350531,'CN0070',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(75,'ST0075','站点75','PR','珠江',118.264439,31.285877,'CN0071',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(76,'ST0076','站点76','HN','黄河',107.982703,38.978162,'CN0072',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(77,'ST0077','站点77','CH','长江',100.566817,36.614037,'CN0073',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(78,'ST0078','站点78','PR','珠江',101.818096,30.106777,'CN0074',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(79,'ST0079','站点79','HN','黄河',115.220212,35.845402,'CN0075',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(80,'ST0080','站点80','CH','长江',115.738755,30.002748,'CN0076',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(81,'ST0081','站点81','PR','珠江',118.070193,33.481557,'CN0077',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(82,'ST0082','站点82','HN','黄河',102.119184,37.875355,'CN0078',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(83,'ST0083','站点83','CH','长江',114.998918,39.577593,'CN0079',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(84,'ST0084','站点84','PR','珠江',112.602586,31.821529,'CN0080',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(85,'ST0085','站点85','HN','黄河',111.028114,32.388680,'CN0081',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(86,'ST0086','站点86','CH','长江',116.244736,31.690700,'CN0082',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(87,'ST0087','站点87','PR','珠江',108.403774,36.536220,'CN0083',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(88,'ST0088','站点88','HN','黄河',103.330724,32.557783,'CN0084',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(89,'ST0089','站点89','CH','长江',107.225769,30.280189,'CN0085',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(90,'ST0090','站点90','PR','珠江',110.739168,32.143654,'CN0086',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(91,'ST0091','站点91','HN','黄河',104.483284,36.316856,'CN0087',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(92,'ST0092','站点92','CH','长江',108.218290,34.288718,'CN0088',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(93,'ST0093','站点93','PR','珠江',107.571014,32.477259,'CN0089',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(94,'ST0094','站点94','HN','黄河',115.260272,33.897854,'CN0090',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(95,'ST0095','站点95','CH','长江',106.588968,37.103981,'CN0091',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(96,'ST0096','站点96','PR','珠江',105.668520,30.472173,'CN0092',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(97,'ST0097','站点97','HN','黄河',112.630727,36.710322,'CN0093',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(98,'ST0098','站点98','CH','长江',110.723288,30.168668,'CN0094',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(99,'ST0099','站点99','PR','珠江',115.063719,33.911745,'CN0095',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(100,'ST0100','站点100','HN','黄河',105.443395,34.433432,'CN0096',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(101,'ST0101','站点101','CH','长江',114.285672,32.355508,'CN0097',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(102,'ST0102','站点102','PR','珠江',118.256985,34.694285,'CN0098',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(103,'ST0103','站点103','HN','黄河',113.951576,33.407824,'CN0099',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(104,'ST0104','站点104','CH','长江',118.936288,31.837265,'CN0100',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(105,'ST0105','站点105','PR','珠江',111.115310,30.292423,'CN0101',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(106,'ST0106','站点106','HN','黄河',108.486527,39.597693,'CN0102',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(107,'ST0107','站点107','CH','长江',108.592285,37.969627,'CN0103',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(108,'ST0108','站点108','PR','珠江',117.436518,37.345689,'CN0104',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(109,'ST0109','站点109','HN','黄河',102.159182,37.717076,'CN0105',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(110,'ST0110','站点110','CH','长江',104.349307,38.719942,'CN0106',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(111,'ST0111','站点111','PR','珠江',108.561722,30.694338,'CN0107',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(112,'ST0112','站点112','HN','黄河',117.109533,33.249846,'CN0108',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(113,'ST0113','站点113','CH','长江',117.861599,33.972281,'CN0109',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(114,'ST0114','站点114','PR','珠江',117.466322,32.499202,'CN0110',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(115,'ST0115','站点115','HN','黄河',116.141895,39.969053,'CN0111',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(116,'ST0116','站点116','CH','长江',100.295393,35.764778,'CN0112',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(117,'ST0117','站点117','PR','珠江',119.291588,37.998846,'CN0113',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(118,'ST0118','站点118','HN','黄河',118.033087,32.276787,'CN0114',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(119,'ST0119','站点119','CH','长江',104.351888,36.904544,'CN0115',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(120,'ST0120','站点120','PR','珠江',116.732133,32.888774,'CN0116',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(121,'ST0121','站点121','HN','黄河',117.140148,35.364413,'CN0117',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(122,'ST0122','站点122','CH','长江',111.260478,39.030456,'CN0118',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(123,'ST0123','站点123','PR','珠江',110.543789,31.445945,'CN0119',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(124,'ST0124','站点124','HN','黄河',105.439464,36.254345,'CN0120',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(125,'ST0125','站点125','CH','长江',110.532055,32.992810,'CN0121',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(126,'ST0126','站点126','PR','珠江',119.587305,33.966922,'CN0122',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(127,'ST0127','站点127','HN','黄河',111.665264,33.295918,'CN0123',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(128,'ST0128','站点128','CH','长江',118.252191,38.235790,'CN0124',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(129,'ST0129','站点129','PR','珠江',102.349603,32.695289,'CN0125',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(130,'ST0130','站点130','HN','黄河',109.202828,36.149671,'CN0126',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(131,'ST0131','站点131','CH','长江',105.513151,31.920618,'CN0127',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(132,'ST0132','站点132','PR','珠江',109.371651,34.728684,'CN0128',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(133,'ST0133','站点133','HN','黄河',112.550831,30.396049,'CN0129',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(134,'ST0134','站点134','CH','长江',111.858830,35.394808,'CN0130',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(135,'ST0135','站点135','PR','珠江',118.650035,35.856057,'CN0131',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(136,'ST0136','站点136','HN','黄河',101.807343,33.479544,'CN0132',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(137,'ST0137','站点137','CH','长江',103.874920,36.547958,'CN0133',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(138,'ST0138','站点138','PR','珠江',118.517256,37.036804,'CN0134',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(139,'ST0139','站点139','HN','黄河',116.128484,35.292217,'CN0135',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(140,'ST0140','站点140','CH','长江',111.209867,37.619275,'CN0136',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(141,'ST0141','站点141','PR','珠江',115.362634,34.650390,'CN0137',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(142,'ST0142','站点142','HN','黄河',100.070868,38.847641,'CN0138',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(143,'ST0143','站点143','CH','长江',100.765244,33.484529,'CN0139',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(144,'ST0144','站点144','PR','珠江',100.350160,34.141923,'CN0140',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(145,'ST0145','站点145','HN','黄河',108.279810,39.009630,'CN0141',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(146,'ST0146','站点146','CH','长江',102.190718,39.387022,'CN0142',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(147,'ST0147','站点147','PR','珠江',100.430363,31.927120,'CN0143',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(148,'ST0148','站点148','HN','黄河',113.378954,37.470651,'CN0144',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(149,'ST0149','站点149','CH','长江',106.317065,39.923295,'CN0145',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(150,'ST0150','站点150','PR','珠江',106.295712,31.367921,'CN0146',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(151,'ST0151','站点151','HN','黄河',110.663857,31.419466,'CN0147',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(152,'ST0152','站点152','CH','长江',118.004264,38.965717,'CN0148',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(153,'ST0153','站点153','PR','珠江',117.349044,34.323760,'CN0149',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(154,'ST0154','站点154','HN','黄河',106.705881,33.240908,'CN0150',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(155,'ST0155','站点155','CH','长江',106.905211,36.167874,'CN0151',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(156,'ST0156','站点156','PR','珠江',105.853635,34.254652,'CN0152',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(157,'ST0157','站点157','HN','黄河',117.571701,32.581549,'CN0153',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(158,'ST0158','站点158','CH','长江',105.232428,37.132318,'CN0154',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(159,'ST0159','站点159','PR','珠江',105.718638,30.885483,'CN0155',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(160,'ST0160','站点160','HN','黄河',106.541777,31.602717,'CN0156',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(161,'ST0161','站点161','CH','长江',100.496232,37.648231,'CN0157',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(162,'ST0162','站点162','PR','珠江',108.774008,31.515480,'CN0158',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(163,'ST0163','站点163','HN','黄河',117.619695,31.489225,'CN0159',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(164,'ST0164','站点164','CH','长江',119.343119,36.192110,'CN0160',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(165,'ST0165','站点165','PR','珠江',103.063474,31.009309,'CN0161',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(166,'ST0166','站点166','HN','黄河',112.930092,32.550855,'CN0162',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(167,'ST0167','站点167','CH','长江',115.456488,38.439465,'CN0163',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(168,'ST0168','站点168','PR','珠江',117.872735,37.141274,'CN0164',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(169,'ST0169','站点169','HN','黄河',119.052704,31.021162,'CN0165',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(170,'ST0170','站点170','CH','长江',105.208707,38.900072,'CN0166',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(171,'ST0171','站点171','PR','珠江',101.771395,38.027806,'CN0167',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(172,'ST0172','站点172','HN','黄河',117.807979,36.371065,'CN0168',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(173,'ST0173','站点173','CH','长江',115.993959,33.122075,'CN0169',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(174,'ST0174','站点174','PR','珠江',105.097210,31.000504,'CN0170',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(175,'ST0175','站点175','HN','黄河',116.166840,30.990793,'CN0171',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(176,'ST0176','站点176','CH','长江',115.437092,31.012414,'CN0172',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(177,'ST0177','站点177','PR','珠江',105.953123,38.135930,'CN0173',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(178,'ST0178','站点178','HN','黄河',115.081184,37.281149,'CN0174',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(179,'ST0179','站点179','CH','长江',119.421349,39.712046,'CN0175',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(180,'ST0180','站点180','PR','珠江',105.699936,33.950530,'CN0176',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(181,'ST0181','站点181','HN','黄河',102.844216,34.428146,'CN0177',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(182,'ST0182','站点182','CH','长江',106.637953,36.607237,'CN0178',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(183,'ST0183','站点183','PR','珠江',104.949761,34.972576,'CN0179',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(184,'ST0184','站点184','HN','黄河',112.389933,30.313540,'CN0180',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(185,'ST0185','站点185','CH','长江',109.333451,32.026953,'CN0181',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(186,'ST0186','站点186','PR','珠江',108.297602,38.360114,'CN0182',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(187,'ST0187','站点187','HN','黄河',100.174109,30.718857,'CN0183',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(188,'ST0188','站点188','CH','长江',110.315163,31.238908,'CN0184',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(189,'ST0189','站点189','PR','珠江',114.934789,31.989127,'CN0185',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(190,'ST0190','站点190','HN','黄河',119.055809,38.990603,'CN0186',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(191,'ST0191','站点191','CH','长江',110.469375,30.640437,'CN0187',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(192,'ST0192','站点192','PR','珠江',109.470527,30.088812,'CN0188',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(193,'ST0193','站点193','HN','黄河',113.556891,32.267807,'CN0189',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(194,'ST0194','站点194','CH','长江',119.739194,36.628172,'CN0190',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(195,'ST0195','站点195','PR','珠江',106.570937,33.483673,'CN0191',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(196,'ST0196','站点196','HN','黄河',104.651739,30.436093,'CN0192',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(197,'ST0197','站点197','CH','长江',103.546496,32.131042,'CN0193',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(198,'ST0198','站点198','PR','珠江',117.365304,33.384895,'CN0194',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(199,'ST0199','站点199','HN','黄河',102.678230,34.975437,'CN0195',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(200,'ST0200','站点200','CH','长江',110.445846,39.605245,'CN0196',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(201,'ST0201','站点201','PR','珠江',116.183437,30.783229,'CN0197',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(202,'ST0202','站点202','HN','黄河',113.105745,38.362311,'CN0198',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(203,'ST0203','站点203','CH','长江',116.961754,32.990386,'CN0199',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(204,'ST0204','站点204','PR','珠江',101.161884,36.328334,'CN0200',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(205,'ST0205','站点205','HN','黄河',109.617642,32.257620,'CN0201',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(206,'ST0206','站点206','CH','长江',113.152401,33.050960,'CN0202',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(207,'ST0207','站点207','PR','珠江',108.778243,39.167678,'CN0203',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(208,'ST0208','站点208','HN','黄河',118.019163,36.163604,'CN0204',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(209,'ST0209','站点209','CH','长江',104.398646,37.876581,'CN0205',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(210,'ST0210','站点210','PR','珠江',103.603060,34.806464,'CN0206',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(211,'ST0211','站点211','HN','黄河',107.621477,34.567764,'CN0207',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(212,'ST0212','站点212','CH','长江',111.789591,33.724926,'CN0208',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(213,'ST0213','站点213','PR','珠江',104.033265,39.617323,'CN0209',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(214,'ST0214','站点214','HN','黄河',105.424199,33.392728,'CN0210',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(215,'ST0215','站点215','CH','长江',107.683833,39.157162,'CN0211',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(216,'ST0216','站点216','PR','珠江',118.094373,36.483149,'CN0212',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(217,'ST0217','站点217','HN','黄河',119.417271,32.574321,'CN0213',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(218,'ST0218','站点218','CH','长江',113.615059,30.201912,'CN0214',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(219,'ST0219','站点219','PR','珠江',118.876370,34.918887,'CN0215',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(220,'ST0220','站点220','HN','黄河',114.684281,30.925772,'CN0216',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(221,'ST0221','站点221','CH','长江',119.522051,32.375661,'CN0217',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(222,'ST0222','站点222','PR','珠江',105.888576,39.692511,'CN0218',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(223,'ST0223','站点223','HN','黄河',100.252885,30.208459,'CN0219',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(224,'ST0224','站点224','CH','长江',105.286648,38.199290,'CN0220',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(225,'ST0225','站点225','PR','珠江',102.377368,33.889066,'CN0221',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(226,'ST0226','站点226','HN','黄河',100.501115,37.830998,'CN0222',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(227,'ST0227','站点227','CH','长江',115.006017,38.610195,'CN0223',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(228,'ST0228','站点228','PR','珠江',112.981112,33.008904,'CN0224',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(229,'ST0229','站点229','HN','黄河',100.176545,30.844379,'CN0225',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(230,'ST0230','站点230','CH','长江',110.304378,32.879138,'CN0226',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(231,'ST0231','站点231','PR','珠江',117.551159,38.995757,'CN0227',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(232,'ST0232','站点232','HN','黄河',107.570050,36.513231,'CN0228',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(233,'ST0233','站点233','CH','长江',100.240504,32.102782,'CN0229',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(234,'ST0234','站点234','PR','珠江',100.659257,39.738204,'CN0230',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(235,'ST0235','站点235','HN','黄河',112.686573,34.746674,'CN0231',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(236,'ST0236','站点236','CH','长江',116.038562,31.227003,'CN0232',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(237,'ST0237','站点237','PR','珠江',119.612268,39.219056,'CN0233',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(238,'ST0238','站点238','HN','黄河',112.542796,30.242993,'CN0234',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(239,'ST0239','站点239','CH','长江',114.415770,39.174846,'CN0235',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(240,'ST0240','站点240','PR','珠江',112.952740,33.532642,'CN0236',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(241,'ST0241','站点241','HN','黄河',115.213701,31.671692,'CN0237',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(242,'ST0242','站点242','CH','长江',118.995871,38.292659,'CN0238',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(243,'ST0243','站点243','PR','珠江',117.740880,31.059779,'CN0239',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(244,'ST0244','站点244','HN','黄河',118.523912,37.302001,'CN0240',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(245,'ST0245','站点245','CH','长江',103.781384,30.676699,'CN0241',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(246,'ST0246','站点246','PR','珠江',103.000545,30.512016,'CN0242',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(247,'ST0247','站点247','HN','黄河',100.583363,38.918602,'CN0243',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(248,'ST0248','站点248','CH','长江',106.070394,30.342441,'CN0244',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(249,'ST0249','站点249','PR','珠江',106.208728,39.854440,'CN0245',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(250,'ST0250','站点250','HN','黄河',117.942521,30.256146,'CN0246',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(251,'ST0251','站点251','CH','长江',114.961111,35.134627,'CN0247',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(252,'ST0252','站点252','PR','珠江',117.737257,33.906804,'CN0248',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(253,'ST0253','站点253','HN','黄河',119.456528,31.184876,'CN0249',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(254,'ST0254','站点254','CH','长江',118.005478,35.202720,'CN0250',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(255,'ST0255','站点255','PR','珠江',106.648615,31.392542,'CN0251',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(256,'ST0256','站点256','HN','黄河',107.588461,34.344738,'CN0252',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(257,'ST0257','站点257','CH','长江',115.854371,37.665088,'CN0253',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(258,'ST0258','站点258','PR','珠江',105.160508,36.520656,'CN0254',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(259,'ST0259','站点259','HN','黄河',100.769536,35.003041,'CN0255',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(260,'ST0260','站点260','CH','长江',114.564663,35.123221,'CN0256',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(261,'ST0261','站点261','PR','珠江',114.400425,39.873969,'CN0257',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(262,'ST0262','站点262','HN','黄河',111.855257,30.660305,'CN0258',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(263,'ST0263','站点263','CH','长江',107.118684,31.646994,'CN0259',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(264,'ST0264','站点264','PR','珠江',114.303879,36.907010,'CN0260',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(265,'ST0265','站点265','HN','黄河',101.577313,32.252509,'CN0261',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(266,'ST0266','站点266','CH','长江',104.073805,33.179567,'CN0262',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(267,'ST0267','站点267','PR','珠江',113.483052,33.829000,'CN0263',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(268,'ST0268','站点268','HN','黄河',108.451102,33.165150,'CN0264',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(269,'ST0269','站点269','CH','长江',100.236932,36.324089,'CN0265',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(270,'ST0270','站点270','PR','珠江',108.129701,37.701916,'CN0266',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(271,'ST0271','站点271','HN','黄河',107.829223,35.902464,'CN0267',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(272,'ST0272','站点272','CH','长江',102.836885,38.857557,'CN0268',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(273,'ST0273','站点273','PR','珠江',114.267232,32.307725,'CN0269',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(274,'ST0274','站点274','HN','黄河',116.370985,32.096501,'CN0270',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(275,'ST0275','站点275','CH','长江',100.947101,35.913059,'CN0271',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(276,'ST0276','站点276','PR','珠江',101.783790,30.156078,'CN0272',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(277,'ST0277','站点277','HN','黄河',114.039110,37.618234,'CN0273',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(278,'ST0278','站点278','CH','长江',111.917447,38.277112,'CN0274',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(279,'ST0279','站点279','PR','珠江',109.987605,31.704752,'CN0275',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(280,'ST0280','站点280','HN','黄河',104.803533,33.867326,'CN0276',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(281,'ST0281','站点281','CH','长江',113.041763,30.600770,'CN0277',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(282,'ST0282','站点282','PR','珠江',110.616761,31.635924,'CN0278',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(283,'ST0283','站点283','HN','黄河',110.304591,33.742297,'CN0279',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(284,'ST0284','站点284','CH','长江',114.076542,36.249980,'CN0280',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(285,'ST0285','站点285','PR','珠江',119.649969,30.513246,'CN0281',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(286,'ST0286','站点286','HN','黄河',109.597539,31.059615,'CN0282',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(287,'ST0287','站点287','CH','长江',108.108951,39.926379,'CN0283',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(288,'ST0288','站点288','PR','珠江',106.359552,32.257133,'CN0284',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(289,'ST0289','站点289','HN','黄河',109.626933,30.248626,'CN0285',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(290,'ST0290','站点290','CH','长江',116.113782,30.719550,'CN0286',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(291,'ST0291','站点291','PR','珠江',109.665458,38.312368,'CN0287',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(292,'ST0292','站点292','HN','黄河',111.345789,37.884645,'CN0288',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(293,'ST0293','站点293','CH','长江',114.751504,36.580119,'CN0289',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(294,'ST0294','站点294','PR','珠江',107.816572,30.384390,'CN0290',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(295,'ST0295','站点295','HN','黄河',104.110628,32.090890,'CN0291',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(296,'ST0296','站点296','CH','长江',110.006166,30.269375,'CN0292',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(297,'ST0297','站点297','PR','珠江',100.868697,33.815805,'CN0293',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(298,'ST0298','站点298','HN','黄河',106.155289,31.872620,'CN0294',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(299,'ST0299','站点299','CH','长江',116.442485,37.448200,'CN0295',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(300,'ST0300','站点300','PR','珠江',101.068860,32.456528,'CN0296',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(301,'ST0301','站点301','HN','黄河',114.697643,30.665243,'CN0297',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(302,'ST0302','站点302','CH','长江',108.302022,35.444127,'CN0298',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(303,'ST0303','站点303','PR','珠江',104.830799,38.996601,'CN0299',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(304,'ST0304','站点304','HN','黄河',101.548503,32.343276,'CN0300',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(305,'ST0305','站点305','CH','长江',112.891212,36.849133,'CN0301',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(306,'ST0306','站点306','PR','珠江',119.809265,31.424102,'CN0302',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(307,'ST0307','站点307','HN','黄河',112.119005,39.349923,'CN0303',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(308,'ST0308','站点308','CH','长江',108.115170,38.206162,'CN0304',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(309,'ST0309','站点309','PR','珠江',109.126743,35.524118,'CN0305',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(310,'ST0310','站点310','HN','黄河',100.152378,39.159183,'CN0306',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(311,'ST0311','站点311','CH','长江',115.236681,39.116340,'CN0307',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(312,'ST0312','站点312','PR','珠江',110.097215,36.432737,'CN0308',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(313,'ST0313','站点313','HN','黄河',105.417453,39.948301,'CN0309',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(314,'ST0314','站点314','CH','长江',116.374714,37.524891,'CN0310',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(315,'ST0315','站点315','PR','珠江',108.214114,31.090570,'CN0311',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(316,'ST0316','站点316','HN','黄河',112.040933,30.603211,'CN0312',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(317,'ST0317','站点317','CH','长江',112.369244,37.979131,'CN0313',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(318,'ST0318','站点318','PR','珠江',116.244713,38.635055,'CN0314',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(319,'ST0319','站点319','HN','黄河',104.588126,37.749757,'CN0315',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(320,'ST0320','站点320','CH','长江',114.003938,34.905853,'CN0316',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(321,'ST0321','站点321','PR','珠江',109.767817,35.973744,'CN0317',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(322,'ST0322','站点322','HN','黄河',101.650672,39.148064,'CN0318',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(323,'ST0323','站点323','CH','长江',113.211023,34.292129,'CN0319',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(324,'ST0324','站点324','PR','珠江',111.138023,39.020212,'CN0320',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(325,'ST0325','站点325','HN','黄河',113.666117,34.452936,'CN0321',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(326,'ST0326','站点326','CH','长江',105.400301,31.621428,'CN0322',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(327,'ST0327','站点327','PR','珠江',107.822141,30.404754,'CN0323',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(328,'ST0328','站点328','HN','黄河',110.621215,36.065153,'CN0324',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(329,'ST0329','站点329','CH','长江',119.052323,31.493414,'CN0325',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(330,'ST0330','站点330','PR','珠江',110.158593,31.735249,'CN0326',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(331,'ST0331','站点331','HN','黄河',115.214162,32.706504,'CN0327',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(332,'ST0332','站点332','CH','长江',103.627612,39.984084,'CN0328',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(333,'ST0333','站点333','PR','珠江',118.213731,36.229002,'CN0329',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(334,'ST0334','站点334','HN','黄河',116.948925,35.886885,'CN0330',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(335,'ST0335','站点335','CH','长江',109.423992,36.384610,'CN0331',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(336,'ST0336','站点336','PR','珠江',112.777505,33.827635,'CN0332',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(337,'ST0337','站点337','HN','黄河',114.072047,34.070003,'CN0333',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(338,'ST0338','站点338','CH','长江',112.458730,37.653180,'CN0334',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(339,'ST0339','站点339','PR','珠江',100.100679,38.927933,'CN0335',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(340,'ST0340','站点340','HN','黄河',101.389210,35.794451,'CN0336',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(341,'ST0341','站点341','CH','长江',115.050758,37.290908,'CN0337',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(342,'ST0342','站点342','PR','珠江',119.332484,30.923322,'CN0338',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(343,'ST0343','站点343','HN','黄河',102.050492,31.057353,'CN0339',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(344,'ST0344','站点344','CH','长江',119.857906,35.603648,'CN0340',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(345,'ST0345','站点345','PR','珠江',113.445406,39.490191,'CN0341',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(346,'ST0346','站点346','HN','黄河',101.701134,32.124491,'CN0342',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(347,'ST0347','站点347','CH','长江',109.931934,39.963457,'CN0343',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(348,'ST0348','站点348','PR','珠江',116.615726,39.797856,'CN0344',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(349,'ST0349','站点349','HN','黄河',112.802745,30.091924,'CN0345',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(350,'ST0350','站点350','CH','长江',119.388543,33.894136,'CN0346',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(351,'ST0351','站点351','PR','珠江',117.171123,37.783946,'CN0347',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(352,'ST0352','站点352','HN','黄河',116.381213,30.913698,'CN0348',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(353,'ST0353','站点353','CH','长江',101.474923,35.537461,'CN0349',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(354,'ST0354','站点354','PR','珠江',118.551894,37.796819,'CN0350',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(355,'ST0355','站点355','HN','黄河',111.390644,33.887970,'CN0351',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(356,'ST0356','站点356','CH','长江',106.785184,32.106911,'CN0352',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(357,'ST0357','站点357','PR','珠江',109.299977,37.106464,'CN0353',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(358,'ST0358','站点358','HN','黄河',113.954424,34.240256,'CN0354',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(359,'ST0359','站点359','CH','长江',109.992600,32.981851,'CN0355',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(360,'ST0360','站点360','PR','珠江',115.963947,35.782220,'CN0356',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(361,'ST0361','站点361','HN','黄河',112.270094,31.410066,'CN0357',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(362,'ST0362','站点362','CH','长江',118.635253,33.614271,'CN0358',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(363,'ST0363','站点363','PR','珠江',109.009280,32.310429,'CN0359',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(364,'ST0364','站点364','HN','黄河',108.339712,32.053469,'CN0360',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(365,'ST0365','站点365','CH','长江',106.084251,38.514951,'CN0361',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(366,'ST0366','站点366','PR','珠江',110.432492,33.858068,'CN0362',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(367,'ST0367','站点367','HN','黄河',105.932503,30.300526,'CN0363',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(368,'ST0368','站点368','CH','长江',113.561962,30.447399,'CN0364',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(369,'ST0369','站点369','PR','珠江',113.641983,34.385694,'CN0365',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(370,'ST0370','站点370','HN','黄河',111.632210,36.155327,'CN0366',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(371,'ST0371','站点371','CH','长江',102.955343,35.877786,'CN0367',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(372,'ST0372','站点372','PR','珠江',114.462431,36.164854,'CN0368',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(373,'ST0373','站点373','HN','黄河',103.820206,38.873672,'CN0369',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(374,'ST0374','站点374','CH','长江',117.957241,32.263305,'CN0370',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(375,'ST0375','站点375','PR','珠江',100.907618,32.385431,'CN0371',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(376,'ST0376','站点376','HN','黄河',113.880272,30.426089,'CN0372',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(377,'ST0377','站点377','CH','长江',119.285174,37.834405,'CN0373',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(378,'ST0378','站点378','PR','珠江',106.176248,34.101232,'CN0374',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(379,'ST0379','站点379','HN','黄河',116.139164,32.543205,'CN0375',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(380,'ST0380','站点380','CH','长江',117.342872,32.952238,'CN0376',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(381,'ST0381','站点381','PR','珠江',119.626796,38.121186,'CN0377',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(382,'ST0382','站点382','HN','黄河',115.804814,38.654960,'CN0378',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(383,'ST0383','站点383','CH','长江',112.702587,31.207092,'CN0379',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(384,'ST0384','站点384','PR','珠江',108.841711,33.472902,'CN0380',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(385,'ST0385','站点385','HN','黄河',102.762697,37.181388,'CN0381',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(386,'ST0386','站点386','CH','长江',119.686803,38.998944,'CN0382',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(387,'ST0387','站点387','PR','珠江',100.409824,30.825112,'CN0383',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(388,'ST0388','站点388','HN','黄河',100.321538,38.300200,'CN0384',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(389,'ST0389','站点389','CH','长江',112.344578,34.772830,'CN0385',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(390,'ST0390','站点390','PR','珠江',110.685577,35.293284,'CN0386',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(391,'ST0391','站点391','HN','黄河',105.684516,30.431792,'CN0387',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(392,'ST0392','站点392','CH','长江',102.005060,37.472514,'CN0388',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(393,'ST0393','站点393','PR','珠江',111.809562,32.921122,'CN0389',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(394,'ST0394','站点394','HN','黄河',117.281103,37.982702,'CN0390',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(395,'ST0395','站点395','CH','长江',116.604845,32.783669,'CN0391',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(396,'ST0396','站点396','PR','珠江',110.304249,37.746556,'CN0392',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(397,'ST0397','站点397','HN','黄河',106.754397,31.654913,'CN0393',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(398,'ST0398','站点398','CH','长江',119.560378,39.105942,'CN0394',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(399,'ST0399','站点399','PR','珠江',116.129146,35.810450,'CN0395',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(400,'ST0400','站点400','HN','黄河',102.823260,36.804276,'CN0396',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(401,'ST0401','站点401','CH','长江',106.470515,32.284481,'CN0397',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(402,'ST0402','站点402','PR','珠江',108.904997,36.618275,'CN0398',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(403,'ST0403','站点403','HN','黄河',108.219257,33.941204,'CN0399',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(404,'ST0404','站点404','CH','长江',105.836882,34.441506,'CN0400',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(405,'ST0405','站点405','PR','珠江',112.763124,37.030130,'CN0401',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(406,'ST0406','站点406','HN','黄河',119.621111,30.936366,'CN0402',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(407,'ST0407','站点407','CH','长江',110.424230,32.062254,'CN0403',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(408,'ST0408','站点408','PR','珠江',118.632335,37.218163,'CN0404',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(409,'ST0409','站点409','HN','黄河',109.587657,33.660358,'CN0405',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(410,'ST0410','站点410','CH','长江',118.823413,32.533952,'CN0406',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(411,'ST0411','站点411','PR','珠江',113.459683,39.873177,'CN0407',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(412,'ST0412','站点412','HN','黄河',101.231135,34.075161,'CN0408',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(413,'ST0413','站点413','CH','长江',100.572055,36.465788,'CN0409',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(414,'ST0414','站点414','PR','珠江',112.076212,34.348398,'CN0410',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(415,'ST0415','站点415','HN','黄河',113.415727,37.229602,'CN0411',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(416,'ST0416','站点416','CH','长江',117.261661,31.761149,'CN0412',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(417,'ST0417','站点417','PR','珠江',103.153533,30.373372,'CN0413',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(418,'ST0418','站点418','HN','黄河',106.611048,33.002338,'CN0414',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(419,'ST0419','站点419','CH','长江',105.449054,31.696247,'CN0415',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(420,'ST0420','站点420','PR','珠江',112.131951,30.562520,'CN0416',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(421,'ST0421','站点421','HN','黄河',117.843602,33.296380,'CN0417',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(422,'ST0422','站点422','CH','长江',111.381711,38.401260,'CN0418',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(423,'ST0423','站点423','PR','珠江',109.162444,37.980926,'CN0419',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(424,'ST0424','站点424','HN','黄河',118.156187,32.793054,'CN0420',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(425,'ST0425','站点425','CH','长江',109.045327,32.919213,'CN0421',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(426,'ST0426','站点426','PR','珠江',103.783938,32.137020,'CN0422',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(427,'ST0427','站点427','HN','黄河',115.347581,35.179987,'CN0423',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(428,'ST0428','站点428','CH','长江',112.917201,38.774959,'CN0424',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(429,'ST0429','站点429','PR','珠江',119.881896,38.032093,'CN0425',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(430,'ST0430','站点430','HN','黄河',116.558464,31.308926,'CN0426',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(431,'ST0431','站点431','CH','长江',101.364751,31.837947,'CN0427',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(432,'ST0432','站点432','PR','珠江',114.901256,37.630931,'CN0428',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(433,'ST0433','站点433','HN','黄河',117.655788,31.259192,'CN0429',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(434,'ST0434','站点434','CH','长江',115.212512,35.868160,'CN0430',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(435,'ST0435','站点435','PR','珠江',118.155620,30.708822,'CN0431',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(436,'ST0436','站点436','HN','黄河',101.320286,33.107284,'CN0432',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(437,'ST0437','站点437','CH','长江',108.842942,39.564790,'CN0433',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(438,'ST0438','站点438','PR','珠江',106.236198,34.397956,'CN0434',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(439,'ST0439','站点439','HN','黄河',105.732992,34.803937,'CN0435',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(440,'ST0440','站点440','CH','长江',117.040626,37.861058,'CN0436',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(441,'ST0441','站点441','PR','珠江',112.936509,31.275555,'CN0437',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(442,'ST0442','站点442','HN','黄河',119.456775,35.361088,'CN0438',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(443,'ST0443','站点443','CH','长江',105.964039,33.820755,'CN0439',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(444,'ST0444','站点444','PR','珠江',110.666248,33.169424,'CN0440',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(445,'ST0445','站点445','HN','黄河',102.989926,33.523053,'CN0441',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(446,'ST0446','站点446','CH','长江',104.064981,38.277859,'CN0442',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(447,'ST0447','站点447','PR','珠江',111.561222,35.369542,'CN0443',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(448,'ST0448','站点448','HN','黄河',104.078340,34.857592,'CN0444',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(449,'ST0449','站点449','CH','长江',107.369180,31.989134,'CN0445',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(450,'ST0450','站点450','PR','珠江',107.164230,32.041942,'CN0446',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(451,'ST0451','站点451','HN','黄河',118.417722,38.549429,'CN0447',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(452,'ST0452','站点452','CH','长江',115.112046,30.391813,'CN0448',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(453,'ST0453','站点453','PR','珠江',102.009326,35.627821,'CN0449',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(454,'ST0454','站点454','HN','黄河',106.134748,35.638031,'CN0450',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(455,'ST0455','站点455','CH','长江',114.771886,38.372730,'CN0451',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(456,'ST0456','站点456','PR','珠江',109.555050,36.546955,'CN0452',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(457,'ST0457','站点457','HN','黄河',105.380448,32.640878,'CN0453',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(458,'ST0458','站点458','CH','长江',101.671166,39.894433,'CN0454',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(459,'ST0459','站点459','PR','珠江',105.157838,38.296100,'CN0455',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(460,'ST0460','站点460','HN','黄河',118.281593,31.833542,'CN0456',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(461,'ST0461','站点461','CH','长江',119.403426,30.400217,'CN0457',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(462,'ST0462','站点462','PR','珠江',107.043518,37.585356,'CN0458',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(463,'ST0463','站点463','HN','黄河',109.697074,34.689429,'CN0459',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(464,'ST0464','站点464','CH','长江',118.089440,32.957007,'CN0460',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(465,'ST0465','站点465','PR','珠江',118.716009,31.238851,'CN0461',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(466,'ST0466','站点466','HN','黄河',110.947949,33.191788,'CN0462',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(467,'ST0467','站点467','CH','长江',100.008486,31.458082,'CN0463',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(468,'ST0468','站点468','PR','珠江',100.686204,39.982323,'CN0464',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(469,'ST0469','站点469','HN','黄河',117.974885,38.073094,'CN0465',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(470,'ST0470','站点470','CH','长江',106.254905,32.936512,'CN0466',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(471,'ST0471','站点471','PR','珠江',111.861970,35.231417,'CN0467',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(472,'ST0472','站点472','HN','黄河',103.275347,30.590067,'CN0468',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(473,'ST0473','站点473','CH','长江',102.289546,38.672232,'CN0469',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(474,'ST0474','站点474','PR','珠江',105.028921,30.327935,'CN0470',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(475,'ST0475','站点475','HN','黄河',111.673356,31.540220,'CN0471',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(476,'ST0476','站点476','CH','长江',106.321068,39.835337,'CN0472',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(477,'ST0477','站点477','PR','珠江',109.418960,35.057897,'CN0473',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(478,'ST0478','站点478','HN','黄河',101.385573,32.379050,'CN0474',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(479,'ST0479','站点479','CH','长江',109.036130,30.228021,'CN0475',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(480,'ST0480','站点480','PR','珠江',119.325233,38.158863,'CN0476',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(481,'ST0481','站点481','HN','黄河',104.022152,34.993911,'CN0477',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(482,'ST0482','站点482','CH','长江',118.067555,36.911995,'CN0478',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(483,'ST0483','站点483','PR','珠江',100.708021,33.975961,'CN0479',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(484,'ST0484','站点484','HN','黄河',113.828362,37.050035,'CN0480',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(485,'ST0485','站点485','CH','长江',104.039026,35.818364,'CN0481',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(486,'ST0486','站点486','PR','珠江',100.063235,31.915313,'CN0482',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(487,'ST0487','站点487','HN','黄河',102.818198,37.268199,'CN0483',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(488,'ST0488','站点488','CH','长江',104.350616,38.724442,'CN0484',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(489,'ST0489','站点489','PR','珠江',107.187849,36.973209,'CN0485',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(490,'ST0490','站点490','HN','黄河',119.157469,33.790145,'CN0486',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(491,'ST0491','站点491','CH','长江',117.533494,34.384578,'CN0487',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(492,'ST0492','站点492','PR','珠江',102.617653,33.453940,'CN0488',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(493,'ST0493','站点493','HN','黄河',101.601014,36.502674,'CN0489',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(494,'ST0494','站点494','CH','长江',106.995845,32.503256,'CN0490',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(495,'ST0495','站点495','PR','珠江',108.019701,34.079552,'CN0491',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(496,'ST0496','站点496','HN','黄河',118.170081,35.775131,'CN0492',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(497,'ST0497','站点497','CH','长江',108.780849,38.520318,'CN0493',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(498,'ST0498','站点498','PR','珠江',101.059941,38.158903,'CN0494',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(499,'ST0499','站点499','HN','黄河',101.256663,35.491123,'CN0495',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(500,'ST0500','站点500','CH','长江',109.334191,38.702529,'CN0496',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(501,'ST0501','站点501','PR','珠江',119.342383,33.185556,'CN0497',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(502,'ST0502','站点502','HN','黄河',107.892590,35.308221,'CN0498',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(503,'ST0503','站点503','CH','长江',102.113537,34.520845,'CN0499',0,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."STATION"("STATION_ID","STATION_CODE","STATION_NAME","BASIN_CODE","RIVER_NAME","LONGITUDE","LATITUDE","ADMIN_REGION_CODE","STATUS","CREATED_AT","UPDATED_AT") VALUES(504,'ST0504','站点504','PR','珠江',102.535519,33.285602,'CN0500',1,TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'),TO_DATE('2025-09-18 16:33:18','YYYY-MM-DD HH24:MI:SS.FF'));

INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(600,'ST0100',TO_DATE('2025-09-01 09:59:00','YYYY-MM-DD HH24:MI:SS.FF'),-3.853,'OK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(601,'ST0101',TO_DATE('2025-09-01 10:00:00','YYYY-MM-DD HH24:MI:SS.FF'),9999,'MAN',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(602,'ST0102',TO_DATE('2025-09-01 10:01:00','YYYY-MM-DD HH24:MI:SS.FF'),-8.081,'CHK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(603,'ST0103',TO_DATE('2025-09-01 10:02:00','YYYY-MM-DD HH24:MI:SS.FF'),9.803,'OK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(604,'ST0104',TO_DATE('2025-09-01 10:03:00','YYYY-MM-DD HH24:MI:SS.FF'),'','CHK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(605,'ST0105',TO_DATE('2025-09-01 10:04:00','YYYY-MM-DD HH24:MI:SS.FF'),'','MISS',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(606,'ST0106',TO_DATE('2025-09-01 10:05:00','YYYY-MM-DD HH24:MI:SS.FF'),6.455,'OK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(607,'ST0107',TO_DATE('2025-09-01 10:06:00','YYYY-MM-DD HH24:MI:SS.FF'),8.269,'MAN',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(608,'ST0108',TO_DATE('2025-09-01 10:07:00','YYYY-MM-DD HH24:MI:SS.FF'),3.825,'CHK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(609,'ST0109',TO_DATE('2025-09-01 10:08:00','YYYY-MM-DD HH24:MI:SS.FF'),6.292,'MISS',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(610,'ST0110',TO_DATE('2025-09-01 10:09:00','YYYY-MM-DD HH24:MI:SS.FF'),'','MAN',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(611,'ST0111',TO_DATE('2025-09-01 10:10:00','YYYY-MM-DD HH24:MI:SS.FF'),'','OK',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));
INSERT INTO "WATER_TP"."WATER_LEVEL"("ID","STATION_CODE","OBS_TIME","WATER_LEVEL","QUALITY_FLAG","TS") VALUES(612,'ST0112',TO_DATE('2025-09-01 10:11:00','YYYY-MM-DD HH24:MI:SS.FF'),'','MISS',TO_DATE('2025-09-18 16:34:57','YYYY-MM-DD HH24:MI:SS.FF'));

ALTER TABLE "WATER_TP"."DISCHARGE" ADD CONSTRAINT "PK_DISCHARGE" PRIMARY KEY("ID") ;

ALTER TABLE "WATER_TP"."RAINFALL" ADD CONSTRAINT "PK_RAINFALL" PRIMARY KEY("ID") ;

ALTER TABLE "WATER_TP"."RESERVOIR_LEVEL" ADD CONSTRAINT "PK_RESERVOIR_LEVEL" PRIMARY KEY("ID") ;

ALTER TABLE "WATER_TP"."STATION" ADD CONSTRAINT "PK_STATION" PRIMARY KEY("STATION_ID") ;

ALTER TABLE "WATER_TP"."STATION_ATTR" ADD CONSTRAINT "PK_STATION_ATTR" PRIMARY KEY("STATION_CODE","ATTR_KEY") ;

ALTER TABLE "WATER_TP"."STATION_THRESHOLD" ADD CONSTRAINT "PK_STATION_THRESHOLD" PRIMARY KEY("STATION_CODE","EFFECTIVE_FROM") ;

ALTER TABLE "WATER_TP"."WARNING_EVENT" ADD CONSTRAINT "PK_WARNING_EVENT" PRIMARY KEY("EVENT_ID") ;

ALTER TABLE "WATER_TP"."WATER_LEVEL" ADD CONSTRAINT "PK_WATER_LEVEL" PRIMARY KEY("ID") ;

ALTER TABLE "WATER_TP"."DEVICE" ADD CONSTRAINT "PK_DEVICE" PRIMARY KEY("DEVICE_ID") ;

ALTER TABLE "WATER_TP"."ADMIN_REGION_DICT" ADD CONSTRAINT "PK_ADMIN_REGION_DICT" PRIMARY KEY("REGION_CODE") ;

ALTER TABLE "WATER_TP"."BASIN_DICT" ADD CONSTRAINT "PK_BASIN_DICT" PRIMARY KEY("BASIN_CODE") ;

CREATE INDEX "IDX_DISCHARGE_STATION_TIME"
ON "WATER_TP"."DISCHARGE"("STATION_CODE","OBS_TIME");

CREATE INDEX "IDX_RAINFALL_STATION_TIME"
ON "WATER_TP"."RAINFALL"("STATION_CODE","OBS_TIME");

CREATE INDEX "IDX_RESERVOIR_STATION_TIME"
ON "WATER_TP"."RESERVOIR_LEVEL"("STATION_CODE","OBS_TIME");

CREATE INDEX "IDX_WL_STATION_TIME"
ON "WATER_TP"."WATER_LEVEL"("STATION_CODE","OBS_TIME");

ALTER TABLE "WATER_TP"."WARNING_EVENT" ADD CHECK(WARNING_LEVEL IN (0, 1, 2, 3)) ENABLE ;

ALTER TABLE "WATER_TP"."DEVICE" ADD CHECK(STATUS IN (0, 1)) ENABLE ;

ALTER TABLE "WATER_TP"."BASIN_DICT" ADD CHECK(STATUS IN (0, 1)) ENABLE ;

COMMENT ON TABLE "WATER_TP"."DISCHARGE" IS '流量观测明细';

COMMENT ON COLUMN "WATER_TP"."DISCHARGE"."DISCHARGE_CMS" IS '流量（立方米/秒）';

COMMENT ON TABLE "WATER_TP"."RAINFALL" IS '降雨观测明细';

COMMENT ON COLUMN "WATER_TP"."RAINFALL"."PRECIP_MM" IS '降雨量（毫米）';

COMMENT ON TABLE "WATER_TP"."RESERVOIR_LEVEL" IS '水库水情明细（位/容/入出流）';

COMMENT ON COLUMN "WATER_TP"."RESERVOIR_LEVEL"."STORAGE_MCM" IS '库容（百万立方米）';

COMMENT ON TABLE "WATER_TP"."STATION" IS '站点基础信息（读源）';

COMMENT ON COLUMN "WATER_TP"."STATION"."STATION_ID" IS '站点主键（源）';

COMMENT ON COLUMN "WATER_TP"."STATION"."STATION_CODE" IS '站点编码（跨源统一关联键）';

COMMENT ON COLUMN "WATER_TP"."STATION"."STATION_NAME" IS '站点名称';

COMMENT ON COLUMN "WATER_TP"."STATION"."BASIN_CODE" IS '流域编码';

COMMENT ON COLUMN "WATER_TP"."STATION"."RIVER_NAME" IS '河流名称';

COMMENT ON COLUMN "WATER_TP"."STATION"."LONGITUDE" IS '经度';

COMMENT ON COLUMN "WATER_TP"."STATION"."LATITUDE" IS '纬度';

COMMENT ON COLUMN "WATER_TP"."STATION"."ADMIN_REGION_CODE" IS '行政区划码';

COMMENT ON COLUMN "WATER_TP"."STATION"."STATUS" IS '1启用/0停用';

COMMENT ON COLUMN "WATER_TP"."STATION"."CREATED_AT" IS '创建时间';

COMMENT ON COLUMN "WATER_TP"."STATION"."UPDATED_AT" IS '修改时间';

COMMENT ON TABLE "WATER_TP"."STATION_ATTR" IS '站点扩展属性（键值对/JSON）';

COMMENT ON COLUMN "WATER_TP"."STATION_ATTR"."STATION_CODE" IS '站点编码';

COMMENT ON COLUMN "WATER_TP"."STATION_ATTR"."ATTR_KEY" IS '属性键';

COMMENT ON COLUMN "WATER_TP"."STATION_ATTR"."ATTR_VALUE" IS '属性值';

COMMENT ON COLUMN "WATER_TP"."STATION_ATTR"."VALUE_TYPE" IS '值类型';

COMMENT ON TABLE "WATER_TP"."STATION_THRESHOLD" IS '站点水位预警阈值（版本区间）';

COMMENT ON COLUMN "WATER_TP"."STATION_THRESHOLD"."EFFECTIVE_FROM" IS '生效起';

COMMENT ON COLUMN "WATER_TP"."STATION_THRESHOLD"."EFFECTIVE_TO" IS '生效止';

COMMENT ON TABLE "WATER_TP"."WATER_LEVEL" IS '水位原始明细（读源）';

COMMENT ON COLUMN "WATER_TP"."WATER_LEVEL"."ID" IS '记录主键（源）';

COMMENT ON COLUMN "WATER_TP"."WATER_LEVEL"."STATION_CODE" IS '站点编码';

COMMENT ON COLUMN "WATER_TP"."WATER_LEVEL"."OBS_TIME" IS '观测时间';

COMMENT ON COLUMN "WATER_TP"."WATER_LEVEL"."WATER_LEVEL" IS '水位（米）';

COMMENT ON COLUMN "WATER_TP"."WATER_LEVEL"."QUALITY_FLAG" IS '源端质控标识';

COMMENT ON COLUMN "WATER_TP"."WATER_LEVEL"."TS" IS '源端变更时间';

COMMENT ON TABLE "WATER_TP"."DEVICE" IS '站点设备台账';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."DEVICE_ID" IS '设备ID';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."STATION_CODE" IS '所属站点编码';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."DEVICE_TYPE" IS '设备类型';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."DEVICE_MODEL" IS '设备型号';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."SERIAL_NO" IS '序列号';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."INSTALL_TIME" IS '安装时间';

COMMENT ON COLUMN "WATER_TP"."DEVICE"."STATUS" IS '1在用/0停用';

COMMENT ON TABLE "WATER_TP"."ADMIN_REGION_DICT" IS '行政区划字典';

COMMENT ON COLUMN "WATER_TP"."ADMIN_REGION_DICT"."REGION_CODE" IS '行政区划码';

COMMENT ON COLUMN "WATER_TP"."ADMIN_REGION_DICT"."REGION_NAME" IS '行政区划名称';

COMMENT ON COLUMN "WATER_TP"."ADMIN_REGION_DICT"."PARENT_CODE" IS '上级行政区划码';

COMMENT ON COLUMN "WATER_TP"."ADMIN_REGION_DICT"."LEVEL_NO" IS '层级';

COMMENT ON COLUMN "WATER_TP"."ADMIN_REGION_DICT"."STATUS" IS '1启用/0停用';

COMMENT ON TABLE "WATER_TP"."BASIN_DICT" IS '流域字典';

COMMENT ON COLUMN "WATER_TP"."BASIN_DICT"."BASIN_CODE" IS '流域编码';

COMMENT ON COLUMN "WATER_TP"."BASIN_DICT"."BASIN_NAME" IS '流域名称';

COMMENT ON COLUMN "WATER_TP"."BASIN_DICT"."PARENT_CODE" IS '上级流域编码';

COMMENT ON COLUMN "WATER_TP"."BASIN_DICT"."LEVEL_NO" IS '层级';

COMMENT ON COLUMN "WATER_TP"."BASIN_DICT"."STATUS" IS '1启用/0停用';

CREATE INDEX "IDX_WARNING_STATION_TIME"
ON "WATER_TP"."WARNING_EVENT"("STATION_CODE","START_TIME");

COMMENT ON TABLE "WATER_TP"."WARNING_EVENT" IS '水位预警事件';

COMMENT ON COLUMN "WATER_TP"."WARNING_EVENT"."WARNING_LEVEL" IS '3红/2黄/1蓝/0无';

COMMENT ON COLUMN "WATER_TP"."WARNING_EVENT"."STATUS" IS '事件状态：active/cleared';

CREATE  TRIGGER "WATER_TP"."TRG_BASIN_DICT_UPT_TS"
 BEFORE  UPDATE
 ON "WATER_TP"."BASIN_DICT"
 referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
CREATE  TRIGGER "WATER_TP"."TRG_DEVICE_UPT_TS"
 BEFORE  UPDATE
 ON "WATER_TP"."DEVICE"
 referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
CREATE  TRIGGER "WATER_TP"."TRG_STATION_ATTR_UPT_TS"
 BEFORE  UPDATE
 ON "WATER_TP"."STATION_ATTR"
 referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
CREATE  TRIGGER "WATER_TP"."TRG_STATION_THRESHOLD_UPT_TS"
 BEFORE  UPDATE
 ON "WATER_TP"."STATION_THRESHOLD"
 referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
CREATE  TRIGGER "WATER_TP"."TRG_WARNING_EVENT_UPT_TS"
 BEFORE  UPDATE
 ON "WATER_TP"."WARNING_EVENT"
 referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
/

-- 插入测试数据（2025-01 到 2025-09，20个测站）
BEGIN
FOR m IN 1..9 LOOP
    FOR sid IN 1..20 LOOP
      INSERT INTO "WATER_TP"."STATION_WATER_MONTH_REPORT"
        ("STATION_ID","STATION_NAME","AVG_WATER_LEVEL","STAT_MONTH")
      VALUES (
        sid,
        '测站' || LPAD(sid,3,'0'),
        TO_CHAR(ROUND(50 + DBMS_RANDOM.VALUE(0,50), 3)),  -- 转成字符串存储
        '2025-' || LPAD(m,2,'0')
      );
END LOOP;
END LOOP;
END;
/


CREATE TABLE "ODS"."ODS_HYD_DISCHARGE"
(
    "ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "SENSOR_ID" VARCHAR(4000) NULL,
    "OBS_TIME" VARCHAR(4000) NULL,
    "OBS_DATE" VARCHAR(4000) NULL,
    "DISCHARGE_M3S" VARCHAR(4000) NULL,
    "QUALITY_CODE" VARCHAR(4000) NULL,
    "SOURCE" VARCHAR(4000) NULL,
    "TRACE_ID" VARCHAR(4000) NULL,
    "EXT_JSON" VARCHAR(4000) NULL,
    "CREATED_AT" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_HYD_WATER_LEVEL"
(
    "ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "SENSOR_ID" VARCHAR(4000) NULL,
    "OBS_TIME" VARCHAR(4000) NULL,
    "OBS_DATE" VARCHAR(4000) NULL,
    "WATER_LEVEL_M" VARCHAR(4000) NULL,
    "QUALITY_CODE" VARCHAR(4000) NULL,
    "SOURCE" VARCHAR(4000) NULL,
    "TRACE_ID" VARCHAR(4000) NULL,
    "EXT_JSON" VARCHAR(4000) NULL,
    "CREATED_AT" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_HYD_WATER_LEVEL_FLINK_STREAM"
(
    "ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "SENSOR_ID" VARCHAR(4000) NULL,
    "OBS_TIME" VARCHAR(4000) NULL,
    "OBS_DATE" VARCHAR(4000) NULL,
    "WATER_LEVEL_M" VARCHAR(4000) NULL,
    "QUALITY_CODE" VARCHAR(4000) NULL,
    "SOURCE" VARCHAR(4000) NULL,
    "TRACE_ID" VARCHAR(4000) NULL,
    "EXT_JSON" VARCHAR(4000) NULL,
    "CREATED_AT" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_STATION"
(
    "STATION_ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "STATION_NAME" VARCHAR(4000) NULL,
    "BASIN_CODE" VARCHAR(4000) NULL,
    "RIVER_NAME" VARCHAR(4000) NULL,
    "LONGITUDE" VARCHAR(4000) NULL,
    "LATITUDE" VARCHAR(4000) NULL,
    "ADMIN_REGION_CODE" VARCHAR(4000) NULL,
    "STATUS" VARCHAR(4000) NULL,
    "CREATED_AT" VARCHAR(4000) NULL,
    "UPDATED_AT" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_STATION_ALL"
(
    "ID" VARCHAR(4000) NULL,
    "SRC_SYSTEM" VARCHAR(4000) NULL,
    "STATION_ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "STATION_NAME" VARCHAR(4000) NULL,
    "BASIN_CODE" VARCHAR(4000) NULL,
    "RIVER_NAME" VARCHAR(4000) NULL,
    "LONGITUDE" VARCHAR(4000) NULL,
    "LATITUDE" VARCHAR(4000) NULL,
    "ADMIN_REGION_CODE" VARCHAR(4000) NULL,
    "STATUS" VARCHAR(4000) NULL,
    "ETL_BATCH_ID" VARCHAR(4000) NULL,
    "ETL_TIME" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_WATER_LEVEL"
(
    "ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "OBS_TIME" VARCHAR(4000) NULL,
    "WATER_LEVEL" VARCHAR(4000) NULL,
    "QUALITY_FLAG" VARCHAR(4000) NULL,
    "TS" VARCHAR(4000) NULL,
    "SRC_SYSTEM" VARCHAR(4000) NULL,
    "SRC_DB" VARCHAR(4000) NULL,
    "SRC_TABLE" VARCHAR(4000) NULL,
    "ETL_BATCH_ID" VARCHAR(4000) NULL,
    "ETL_TIME" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_WR_STATION"
(
    "STATION_ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "STATION_NAME" VARCHAR(4000) NULL,
    "BASIN_CODE" VARCHAR(4000) NULL,
    "RIVER_NAME" VARCHAR(4000) NULL,
    "LONGITUDE" VARCHAR(4000) NULL,
    "LATITUDE" VARCHAR(4000) NULL,
    "ADMIN_REGION_CODE" VARCHAR(4000) NULL,
    "STATUS" VARCHAR(4000) NULL,
    "CREATED_AT" VARCHAR(4000) NULL,
    "UPDATED_AT" VARCHAR(4000) NULL
);
CREATE TABLE "ODS"."ODS_WR_WATER_LEVEL"
(
    "ID" VARCHAR(4000) NULL,
    "STATION_CODE" VARCHAR(4000) NULL,
    "OBS_TIME" VARCHAR(4000) NULL,
    "WATER_LEVEL" VARCHAR(4000) NULL,
    "QUALITY_FLAG" VARCHAR(4000) NULL,
    "TS" VARCHAR(4000) NULL
);

-- ===========================================================
-- ODS 全量初始化脚本（共 4 张表，每表 1000 条）
-- 适配达梦 DM8，字段类型全为 VARCHAR(4000)
-- ===========================================================

-- 1. ODS_HYD_DISCHARGE（水文监测流量时序数据）
BEGIN
FOR i IN 1..1000 LOOP
    INSERT INTO ODS.ODS_HYD_DISCHARGE (
      ID, STATION_CODE, SENSOR_ID, OBS_TIME, OBS_DATE,
      DISCHARGE_M3S, QUALITY_CODE, SOURCE, TRACE_ID, EXT_JSON, CREATED_AT
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i, 4, '0'),
      TO_CHAR(1000 + i),
      TO_CHAR(SYSDATE - MOD(i, 30), 'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE - MOD(i, 30), 'YYYY-MM-DD'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(10, 500), 3)),
      'Q' || MOD(i, 9),
      'SYSTEM_FLOW',
      'TRACE' || LPAD(i, 6, '0'),
      '{"info":"flow discharge test"}',
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 2. ODS_HYD_WATER_LEVEL（水文监测水位时序数据）
BEGIN
FOR i IN 1..1000 LOOP
    INSERT INTO ODS.ODS_HYD_WATER_LEVEL (
      ID, STATION_CODE, SENSOR_ID, OBS_TIME, OBS_DATE,
      WATER_LEVEL_M, QUALITY_CODE, SOURCE, TRACE_ID, EXT_JSON, CREATED_AT
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i, 4, '0'),
      TO_CHAR(2000 + i),
      TO_CHAR(SYSDATE - MOD(i, 30), 'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE - MOD(i, 30), 'YYYY-MM-DD'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 10), 3)),
      'L' || MOD(i, 9),
      'SYSTEM_LEVEL',
      'TRACE' || LPAD(i, 6, '0'),
      '{"info":"water level test"}',
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 3. ODS_WR_STATION（水资源站点基础信息）
BEGIN
FOR i IN 1..1000 LOOP
    INSERT INTO ODS.ODS_WR_STATION (
      STATION_ID, STATION_CODE, STATION_NAME,
      BASIN_CODE, RIVER_NAME, LONGITUDE, LATITUDE,
      ADMIN_REGION_CODE, STATUS, CREATED_AT, UPDATED_AT
    ) VALUES (
      TO_CHAR(i),
      'WR' || LPAD(i, 4, '0'),
      '水文站点_' || TO_CHAR(i),
      'BS' || LPAD(MOD(i, 10), 2, '0'),
      '河流_' || TO_CHAR(MOD(i, 20)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(100, 120), 6)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(20, 40), 6)),
      'CN' || LPAD(MOD(i, 100), 4, '0'),
      CASE WHEN MOD(i, 2) = 0 THEN '1' ELSE '0' END,
      TO_CHAR(SYSDATE - MOD(i, 10), 'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 4. ODS_WR_WATER_LEVEL（水资源水位原始明细）
BEGIN
FOR i IN 1..1000 LOOP
    INSERT INTO ODS.ODS_WR_WATER_LEVEL (
      ID, STATION_CODE, OBS_TIME, WATER_LEVEL, QUALITY_FLAG, TS
    ) VALUES (
      TO_CHAR(i),
      'WR' || LPAD(i, 4, '0'),
      TO_CHAR(SYSDATE - MOD(i, 30), 'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.1, 20), 3)),
      'F' || MOD(i, 5),
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- ===========================================================
-- ✅ 初始化完成：共 4000 条记录
-- ===========================================================


COMMENT ON TABLE "ODS"."ODS_HYD_DISCHARGE" IS '水文监测流量时序数据';

COMMENT ON TABLE "ODS"."ODS_HYD_WATER_LEVEL" IS '水文监测水位时序数据';

COMMENT ON TABLE "ODS"."ODS_HYD_WATER_LEVEL_FLINK_STREAM" IS '水位时序数据（Flink Stream）';

COMMENT ON TABLE "ODS"."ODS_STATION" IS '站点基础信息表';

COMMENT ON TABLE "ODS"."ODS_STATION_ALL" IS '水资源多源站点汇总（追加/重建）';

COMMENT ON TABLE "ODS"."ODS_WATER_LEVEL" IS '水资源水位原始库';

COMMENT ON TABLE "ODS"."ODS_WR_STATION" IS '水资源站点基础信息';

COMMENT ON TABLE "ODS"."ODS_WR_WATER_LEVEL" IS '水资源水位原始明细';

-- ===========================================================
-- 1. DWD_DIM_STATION — 统一站点维表
-- ===========================================================
CREATE TABLE DWD.DWD_DIM_STATION (
                                     STATION_ID         VARCHAR(4000) NULL,
                                     STATION_CODE       VARCHAR(4000) NULL,
                                     STATION_NAME       VARCHAR(4000) NULL,
                                     BASIN_CODE         VARCHAR(4000) NULL,
                                     BASIN_NAME         VARCHAR(4000) NULL,
                                     RIVER_NAME         VARCHAR(4000) NULL,
                                     LONGITUDE          VARCHAR(4000) NULL,
                                     LATITUDE           VARCHAR(4000) NULL,
                                     ADMIN_REGION_CODE  VARCHAR(4000) NULL,
                                     STATUS             VARCHAR(4000) NULL,
                                     EFFECTIVE_FROM     VARCHAR(4000) NULL,
                                     EFFECTIVE_TO       VARCHAR(4000) NULL,
                                     ETL_TIME           VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_DIM_STATION IS '统一站点维表';


-- ===========================================================
-- 2. DWD_FACT_WATER_LEVEL_CLEAN — 清洗后的水位明细
-- ===========================================================
CREATE TABLE DWD.DWD_FACT_WATER_LEVEL_CLEAN (
                                                STATION_ID       VARCHAR(4000) NULL,
                                                STATION_CODE     VARCHAR(4000) NULL,
                                                OBS_TIME         VARCHAR(4000) NULL,
                                                WATER_LEVEL      VARCHAR(4000) NULL,
                                                ANOMALY_FLAG     VARCHAR(4000) NULL,
                                                IMPUTE_FLAG      VARCHAR(4000) NULL,
                                                QUALITY_FLAG_STD VARCHAR(4000) NULL,
                                                SRC_SYSTEM       VARCHAR(4000) NULL,
                                                ETL_TIME         VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_FACT_WATER_LEVEL_CLEAN IS '清洗后的水位明细';


-- ===========================================================
-- 3. DWD_FACT_WATER_LEVEL_DEDUP — 去重后的水位明细
-- ===========================================================
CREATE TABLE DWD.DWD_FACT_WATER_LEVEL_DEDUP (
                                                STATION_ID   VARCHAR(4000) NULL,
                                                STATION_CODE VARCHAR(4000) NULL,
                                                OBS_TIME     VARCHAR(4000) NULL,
                                                WATER_LEVEL  VARCHAR(4000) NULL,
                                                SELECT_RULE  VARCHAR(4000) NULL,
                                                ETL_TIME     VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_FACT_WATER_LEVEL_DEDUP IS '去重后的水位明细';


-- ===========================================================
-- 4. DWD_FACT_WATER_LEVEL_SPLIT — 字段拆分后的水位明细
-- ===========================================================
CREATE TABLE DWD.DWD_FACT_WATER_LEVEL_SPLIT (
                                                STATION_ID        VARCHAR(4000) NULL,
                                                STATION_CODE      VARCHAR(4000) NULL,
                                                OBS_TIME          VARCHAR(4000) NULL,
                                                WATER_LEVEL       VARCHAR(4000) NULL,
                                                BASIN_CODE        VARCHAR(4000) NULL,
                                                ADMIN_REGION_CODE VARCHAR(4000) NULL,
                                                ETL_TIME          VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_FACT_WATER_LEVEL_SPLIT IS '字段拆分后的水位明细';


-- ===========================================================
-- 5. DWD_FACT_WATER_LEVEL_UNPIVOT — 行列转置中间层
-- ===========================================================
CREATE TABLE DWD.DWD_FACT_WATER_LEVEL_UNPIVOT (
                                                  RID          VARCHAR(4000) NULL,
                                                  STATION_CODE VARCHAR(4000) NULL,
                                                  OBS_TIME     VARCHAR(4000) NULL,
                                                  METRIC_NAME  VARCHAR(4000) NULL,
                                                  METRIC_VALUE VARCHAR(4000) NULL,
                                                  ETL_TIME     VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_FACT_WATER_LEVEL_UNPIVOT IS '行列转置中间层（可选）';


-- ===========================================================
-- 6. DWD_STATION_MERGE — 站点基础信息（含经纬度整合）
-- ===========================================================
CREATE TABLE DWD.DWD_STATION_MERGE (
                                       STATION_ID        VARCHAR(4000) NULL,
                                       STATION_CODE      VARCHAR(4000) NULL,
                                       STATION_NAME      VARCHAR(4000) NULL,
                                       BASIN_CODE        VARCHAR(4000) NULL,
                                       RIVER_NAME        VARCHAR(4000) NULL,
                                       LONGITUDE         VARCHAR(4000) NULL,
                                       LATITUDE          VARCHAR(4000) NULL,
                                       ADMIN_REGION_CODE VARCHAR(4000) NULL,
                                       STATUS            VARCHAR(4000) NULL,
                                       CREATED_AT        VARCHAR(4000) NULL,
                                       UPDATED_AT        VARCHAR(4000) NULL,
                                       LNGLAT            VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_STATION_MERGE IS 'DWD_站点基础信息（含经纬度整合字段）';


-- ===========================================================
-- 7. DWD_STATION_WATER_MONTH_REPORT — 站点水位月报表（明细表）
-- ===========================================================
CREATE TABLE DWD.DWD_STATION_WATER_MONTH_REPORT (
                                                    STATION_ID       VARCHAR(4000) NULL,
                                                    STATION_NAME     VARCHAR(4000) NULL,
                                                    AVG_WATER_LEVEL  VARCHAR(4000) NULL,
                                                    STAT_MONTH       VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_STATION_WATER_MONTH_REPORT IS '站点水位月报表（明细表）';


-- ===========================================================
-- 8. DWD_STATION_WATER_MONTH_REPORT_WIDE — 站点水位月报宽表
-- ===========================================================
CREATE TABLE DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE (
                                                         STATION_ID VARCHAR(4000) NULL,
                                                         STATION_NAME VARCHAR(4000) NULL,
                                                         "2025_01" VARCHAR(4000) NULL,
                                                         "2025_02" VARCHAR(4000) NULL,
                                                         "2025_03" VARCHAR(4000) NULL,
                                                         "2025_04" VARCHAR(4000) NULL,
                                                         "2025_05" VARCHAR(4000) NULL,
                                                         "2025_06" VARCHAR(4000) NULL,
                                                         "2025_07" VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE IS '站点水位月报宽表（2025-01 至 2025-07）';


-- ===========================================================
-- 9. DWD_WATER_LEVEL_CLEAN_MISSING — 缺失值补全
-- ===========================================================
CREATE TABLE DWD.DWD_WATER_LEVEL_CLEAN_MISSING (
                                                   ID            VARCHAR(4000) NULL,
                                                   STATION_CODE  VARCHAR(4000) NULL,
                                                   OBS_TIME      VARCHAR(4000) NULL,
                                                   WATER_LEVEL   VARCHAR(4000) NULL,
                                                   QUALITY_FLAG  VARCHAR(4000) NULL,
                                                   TS            VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_WATER_LEVEL_CLEAN_MISSING IS '站点水位明细表（缺失值补全）';


-- ===========================================================
-- 10. DWD_WATER_LEVEL_CLEAN_OUTLIER — 异常值剔除
-- ===========================================================
CREATE TABLE DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER (
                                                   ID            VARCHAR(4000) NULL,
                                                   STATION_CODE  VARCHAR(4000) NULL,
                                                   OBS_TIME      VARCHAR(4000) NULL,
                                                   WATER_LEVEL   VARCHAR(4000) NULL,
                                                   QUALITY_FLAG  VARCHAR(4000) NULL,
                                                   TS            VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER IS '站点水位明细表（异常值剔除）';


-- ===========================================================
-- 11. DWD_WATER_LEVEL_CLEAN_STD — 格式标准化
-- ===========================================================
CREATE TABLE DWD.DWD_WATER_LEVEL_CLEAN_STD (
                                               ID            VARCHAR(4000) NULL,
                                               STATION_CODE  VARCHAR(4000) NULL,
                                               OBS_TIME      VARCHAR(4000) NULL,
                                               WATER_LEVEL   VARCHAR(4000) NULL,
                                               QUALITY_FLAG  VARCHAR(4000) NULL,
                                               TS            VARCHAR(4000) NULL
);
COMMENT ON TABLE DWD.DWD_WATER_LEVEL_CLEAN_STD IS '站点水位明细表（格式标准化，水位值保留2位小数）';

/* ===========================================================
   字段注释补丁（DM8）—— 在表已创建后执行
   =========================================================== */

/* 1) DWD_DIM_STATION */
COMMENT ON COLUMN DWD.DWD_DIM_STATION.STATION_ID        IS '站点维ID（中台分配）';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.STATION_CODE      IS '站点编码（业务键）';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.STATION_NAME      IS '站点名称';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.BASIN_CODE        IS '流域编码';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.BASIN_NAME        IS '流域名称（字典映射）';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.RIVER_NAME        IS '河流名称';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.LONGITUDE         IS '经度';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.LATITUDE          IS '纬度';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.ADMIN_REGION_CODE IS '行政区划码';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.STATUS            IS '1启用/0停用';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.EFFECTIVE_FROM    IS '生效起';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.EFFECTIVE_TO      IS '生效止';
COMMENT ON COLUMN DWD.DWD_DIM_STATION.ETL_TIME          IS '入仓时间';

/* 2) DWD_FACT_WATER_LEVEL_CLEAN */
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.STATION_ID       IS '维表ID（可空后补）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.STATION_CODE     IS '站点编码';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.OBS_TIME         IS '观测时间';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.WATER_LEVEL      IS '水位（米，清洗后值）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.ANOMALY_FLAG     IS '异常标记：0正常/1异常';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.IMPUTE_FLAG      IS '缺失补全标记：1补值';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.QUALITY_FLAG_STD IS '统一质控码';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.SRC_SYSTEM       IS '来源系统';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_CLEAN.ETL_TIME         IS '入仓时间';

/* 3) DWD_FACT_WATER_LEVEL_DEDUP */
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_DEDUP.STATION_ID   IS '维表ID';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_DEDUP.STATION_CODE IS '站点编码';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_DEDUP.OBS_TIME     IS '观测时间';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_DEDUP.WATER_LEVEL  IS '水位（米）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_DEDUP.SELECT_RULE  IS '选择规则（latest_ts/best_quality）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_DEDUP.ETL_TIME     IS '入仓时间';

/* 4) DWD_FACT_WATER_LEVEL_SPLIT */
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.STATION_ID        IS '维表ID';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.STATION_CODE      IS '站点编码';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.OBS_TIME          IS '观测时间';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.WATER_LEVEL       IS '水位（米）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.BASIN_CODE        IS '流域编码（拆分/映射）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.ADMIN_REGION_CODE IS '行政区划（拆分/映射）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_SPLIT.ETL_TIME          IS '入仓时间';

/* 5) DWD_FACT_WATER_LEVEL_UNPIVOT */
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_UNPIVOT.RID          IS '技术主键（为满足首列非 STRING 要求）';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_UNPIVOT.STATION_CODE IS '站点编码';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_UNPIVOT.OBS_TIME     IS '观测时间';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_UNPIVOT.METRIC_NAME  IS '指标名';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_UNPIVOT.METRIC_VALUE IS '指标值';
COMMENT ON COLUMN DWD.DWD_FACT_WATER_LEVEL_UNPIVOT.ETL_TIME     IS '入仓时间';

/* 6) DWD_STATION_MERGE */
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.STATION_ID        IS '站点主键（源）';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.STATION_CODE      IS '站点编码（跨源统一关联键）';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.STATION_NAME      IS '站点名称';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.BASIN_CODE        IS '流域编码';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.RIVER_NAME        IS '河流名称';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.LONGITUDE         IS '经度';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.LATITUDE          IS '纬度';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.ADMIN_REGION_CODE IS '行政区划码';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.STATUS            IS '1启用/0停用';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.CREATED_AT        IS '创建时间';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.UPDATED_AT        IS '修改时间';
COMMENT ON COLUMN DWD.DWD_STATION_MERGE.LNGLAT            IS '经纬度整合，格式：lng,lat 例如 120.123456,30.123456';

/* 7) DWD_STATION_WATER_MONTH_REPORT */
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT.STATION_ID      IS '测站id';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT.STATION_NAME    IS '测站名称';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT.AVG_WATER_LEVEL IS '月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT.STAT_MONTH      IS '统计时间（yyyy-mm，例如：2025-01）';

/* 8) DWD_STATION_WATER_MONTH_REPORT_WIDE */
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE.STATION_ID   IS '测站ID';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE.STATION_NAME IS '测站名称';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_01"    IS '2025-01 月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_02"    IS '2025-02 月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_03"    IS '2025-03 月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_04"    IS '2025-04 月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_05"    IS '2025-05 月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_06"    IS '2025-06 月平均水位';
COMMENT ON COLUMN DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE."2025_07"    IS '2025-07 月平均水位';

/* 9) DWD_WATER_LEVEL_CLEAN_MISSING */
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_MISSING.ID           IS '记录主键（源）';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_MISSING.STATION_CODE IS '站点编码';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_MISSING.OBS_TIME     IS '观测时间';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_MISSING.WATER_LEVEL  IS '水位（米，保留2位小数）';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_MISSING.QUALITY_FLAG IS '源端质控标识';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_MISSING.TS           IS '源端变更时间';

/* 10) DWD_WATER_LEVEL_CLEAN_OUTLIER */
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER.ID           IS '记录主键（源）';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER.STATION_CODE IS '站点编码';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER.OBS_TIME     IS '观测时间';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER.WATER_LEVEL  IS '水位（米，保留2位小数）';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER.QUALITY_FLAG IS '源端质控标识';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER.TS           IS '源端变更时间';

/* 11) DWD_WATER_LEVEL_CLEAN_STD */
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_STD.ID           IS '记录主键（源）';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_STD.STATION_CODE IS '站点编码';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_STD.OBS_TIME     IS '观测时间';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_STD.WATER_LEVEL  IS '水位（米，保留2位小数）';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_STD.QUALITY_FLAG IS '源端质控标识';
COMMENT ON COLUMN DWD.DWD_WATER_LEVEL_CLEAN_STD.TS           IS '源端变更时间';


-- ===========================================================
-- DWD 层初始化随机数据（每表 500 条）
-- ===========================================================

-- 1. DWD_DIM_STATION — 统一站点维表
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_DIM_STATION (
      STATION_ID, STATION_CODE, STATION_NAME, BASIN_CODE, BASIN_NAME,
      RIVER_NAME, LONGITUDE, LATITUDE, ADMIN_REGION_CODE, STATUS,
      EFFECTIVE_FROM, EFFECTIVE_TO, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i, 4, '0'),
      '站点_' || TO_CHAR(i),
      'BS' || LPAD(MOD(i,10), 2, '0'),
      '流域_' || TO_CHAR(MOD(i,10)),
      '河流_' || TO_CHAR(MOD(i,20)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(100,120),6)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(20,40),6)),
      'CN' || LPAD(MOD(i,100),4,'0'),
      CASE WHEN MOD(i,2)=0 THEN '1' ELSE '0' END,
      TO_CHAR(SYSDATE - MOD(i,100),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE + MOD(i,30),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 2. DWD_FACT_WATER_LEVEL_CLEAN
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_FACT_WATER_LEVEL_CLEAN (
      STATION_ID, STATION_CODE, OBS_TIME, WATER_LEVEL,
      ANOMALY_FLAG, IMPUTE_FLAG, QUALITY_FLAG_STD, SRC_SYSTEM, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,30),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      CASE WHEN MOD(i,10)=0 THEN '1' ELSE '0' END,
      CASE WHEN MOD(i,15)=0 THEN '1' ELSE '0' END,
      'Q' || MOD(i,9),
      'SYS_CLEAN',
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 3. DWD_FACT_WATER_LEVEL_DEDUP
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_FACT_WATER_LEVEL_DEDUP (
      STATION_ID, STATION_CODE, OBS_TIME, WATER_LEVEL, SELECT_RULE, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,20),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      CASE WHEN MOD(i,2)=0 THEN 'latest_ts' ELSE 'best_quality' END,
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 4. DWD_FACT_WATER_LEVEL_SPLIT
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_FACT_WATER_LEVEL_SPLIT (
      STATION_ID, STATION_CODE, OBS_TIME, WATER_LEVEL, BASIN_CODE, ADMIN_REGION_CODE, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,15),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      'BS' || LPAD(MOD(i,10),2,'0'),
      'CN' || LPAD(MOD(i,100),4,'0'),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 5. DWD_FACT_WATER_LEVEL_UNPIVOT
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_FACT_WATER_LEVEL_UNPIVOT (
      RID, STATION_CODE, OBS_TIME, METRIC_NAME, METRIC_VALUE, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,10),'YYYY-MM-DD HH24:MI:SS'),
      CASE WHEN MOD(i,2)=0 THEN 'WATER_LEVEL' ELSE 'FLOW' END,
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,1000),3)),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 6. DWD_STATION_MERGE
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_STATION_MERGE (
      STATION_ID, STATION_CODE, STATION_NAME, BASIN_CODE, RIVER_NAME,
      LONGITUDE, LATITUDE, ADMIN_REGION_CODE, STATUS, CREATED_AT, UPDATED_AT, LNGLAT
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      '站点_' || TO_CHAR(i),
      'BS' || LPAD(MOD(i,10),2,'0'),
      '河流_' || TO_CHAR(MOD(i,20)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(100,120),6)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(20,40),6)),
      'CN' || LPAD(MOD(i,100),4,'0'),
      CASE WHEN MOD(i,2)=0 THEN '1' ELSE '0' END,
      TO_CHAR(SYSDATE - MOD(i,10),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(100,120),6)) || ',' || TO_CHAR(ROUND(DBMS_RANDOM.VALUE(20,40),6))
    );
END LOOP;
END;
/
COMMIT;
/

-- 7. DWD_STATION_WATER_MONTH_REPORT
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_STATION_WATER_MONTH_REPORT (
      STATION_ID, STATION_NAME, AVG_WATER_LEVEL, STAT_MONTH
    ) VALUES (
      TO_CHAR(i),
      '站点_' || TO_CHAR(i),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      '2025-' || LPAD(MOD(i,12)+1,2,'0')
    );
END LOOP;
END;
/
COMMIT;
/

-- 8. DWD_STATION_WATER_MONTH_REPORT_WIDE
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_STATION_WATER_MONTH_REPORT_WIDE (
      STATION_ID, STATION_NAME, "2025_01","2025_02","2025_03","2025_04","2025_05","2025_06","2025_07"
    ) VALUES (
      TO_CHAR(i),
      '站点_' || TO_CHAR(i),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3))
    );
END LOOP;
END;
/
COMMIT;
/

-- 9. DWD_WATER_LEVEL_CLEAN_MISSING
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_WATER_LEVEL_CLEAN_MISSING (
      ID, STATION_CODE, OBS_TIME, WATER_LEVEL, QUALITY_FLAG, TS
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,15),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      'F' || MOD(i,5),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 10. DWD_WATER_LEVEL_CLEAN_OUTLIER
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_WATER_LEVEL_CLEAN_OUTLIER (
      ID, STATION_CODE, OBS_TIME, WATER_LEVEL, QUALITY_FLAG, TS
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,15),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),3)),
      'F' || MOD(i,5),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 11. DWD_WATER_LEVEL_CLEAN_STD
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWD.DWD_WATER_LEVEL_CLEAN_STD (
      ID, STATION_CODE, OBS_TIME, WATER_LEVEL, QUALITY_FLAG, TS
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i,4,'0'),
      TO_CHAR(SYSDATE - MOD(i,15),'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0,10),2)),
      'F' || MOD(i,5),
      TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- ✅ 全部 11 张表初始化完成，每表 500 条，共 5500 条


    -- ===========================================================
-- DWS_STATION_WATER_DAY_REPORT — 站点水位日报表
-- ===========================================================
CREATE TABLE DWS.DWS_STATION_WATER_DAY_REPORT (
                                                  STATION_ID    VARCHAR(4000) NULL,
                                                  STATION_NAME  VARCHAR(4000) NULL,
                                                  AVG_LEVEL_YEAR VARCHAR(4000) NULL,
                                                  STAT_DATE     VARCHAR(4000) NULL
);
COMMENT ON TABLE DWS.DWS_STATION_WATER_DAY_REPORT IS '站点水位日报表';
COMMENT ON COLUMN DWS.DWS_STATION_WATER_DAY_REPORT.STATION_ID     IS '测站ID';
COMMENT ON COLUMN DWS.DWS_STATION_WATER_DAY_REPORT.STATION_NAME   IS '测站名称';
COMMENT ON COLUMN DWS.DWS_STATION_WATER_DAY_REPORT.AVG_LEVEL_YEAR IS '年平均水位(米)';
COMMENT ON COLUMN DWS.DWS_STATION_WATER_DAY_REPORT.STAT_DATE      IS '统计日期';

-- ===========================================================
-- DWS_WATER_LEVEL_DAILY_AGG — 水位日级聚合
-- ===========================================================
CREATE TABLE DWS.DWS_WATER_LEVEL_DAILY_AGG (
                                               STAT_DATE      VARCHAR(4000) NULL,
                                               STATION_ID     VARCHAR(4000) NULL,
                                               STATION_CODE   VARCHAR(4000) NULL,
                                               AVG_LEVEL      VARCHAR(4000) NULL,
                                               P95_LEVEL      VARCHAR(4000) NULL,
                                               MAX_LEVEL      VARCHAR(4000) NULL,
                                               MIN_LEVEL      VARCHAR(4000) NULL,
                                               FIRST_OBS_TIME VARCHAR(4000) NULL,
                                               LAST_OBS_TIME  VARCHAR(4000) NULL,
                                               ETL_TIME       VARCHAR(4000) NULL
);
COMMENT ON TABLE DWS.DWS_WATER_LEVEL_DAILY_AGG IS '水位日级聚合';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.STAT_DATE       IS '统计日期';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.STATION_ID      IS '维表ID';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.STATION_CODE    IS '站点编码';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.AVG_LEVEL       IS '日均';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.P95_LEVEL       IS 'P95';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.MAX_LEVEL       IS '日最大';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.MIN_LEVEL       IS '日最小';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.FIRST_OBS_TIME  IS '当日首条时间';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.LAST_OBS_TIME   IS '当日末条时间';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_DAILY_AGG.ETL_TIME        IS '入仓时间';

-- ===========================================================
-- DWS_WATER_LEVEL_HOURLY_AGG — 水位小时级聚合
-- ===========================================================
CREATE TABLE DWS.DWS_WATER_LEVEL_HOURLY_AGG (
                                                STATION_ID    VARCHAR(4000) NULL,
                                                STATION_CODE  VARCHAR(4000) NULL,
                                                HOUR_WINDOW   VARCHAR(4000) NULL,
                                                AVG_LEVEL     VARCHAR(4000) NULL,
                                                MIN_LEVEL     VARCHAR(4000) NULL,
                                                MAX_LEVEL     VARCHAR(4000) NULL,
                                                CNT           VARCHAR(4000) NULL,
                                                COMPUTE_MODE  VARCHAR(4000) NULL,
                                                ETL_TIME      VARCHAR(4000) NULL
);
COMMENT ON TABLE DWS.DWS_WATER_LEVEL_HOURLY_AGG IS '水位小时级聚合';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.STATION_ID    IS '维表ID';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.STATION_CODE  IS '站点编码';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.HOUR_WINDOW   IS '小时窗口起点';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.AVG_LEVEL     IS '均值';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.MIN_LEVEL     IS '最小值';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.MAX_LEVEL     IS '最大值';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.CNT           IS '样本数';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.COMPUTE_MODE  IS 'batch/stream';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_HOURLY_AGG.ETL_TIME      IS '入仓时间';

-- ===========================================================
-- DWS_WATER_LEVEL_WARNING_LVL — 水位预警分级
-- ===========================================================
CREATE TABLE DWS.DWS_WATER_LEVEL_WARNING_LVL (
                                                 STATION_ID       VARCHAR(4000) NULL,
                                                 STATION_CODE     VARCHAR(4000) NULL,
                                                 OBS_TIME         VARCHAR(4000) NULL,
                                                 WATER_LEVEL      VARCHAR(4000) NULL,
                                                 WARNING_LEVEL    VARCHAR(4000) NULL,
                                                 THRESHOLD_BLUE   VARCHAR(4000) NULL,
                                                 THRESHOLD_YELLOW VARCHAR(4000) NULL,
                                                 THRESHOLD_RED    VARCHAR(4000) NULL,
                                                 ETL_TIME         VARCHAR(4000) NULL
);
COMMENT ON TABLE DWS.DWS_WATER_LEVEL_WARNING_LVL IS '水位预警分级';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.STATION_ID        IS '维表ID';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.STATION_CODE      IS '站点编码';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.OBS_TIME          IS '观测时间';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.WATER_LEVEL       IS '水位（米）';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.WARNING_LEVEL     IS '3红/2黄/1蓝/0无';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.THRESHOLD_BLUE    IS '蓝阈值';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.THRESHOLD_YELLOW  IS '黄阈值';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.THRESHOLD_RED     IS '红阈值';
COMMENT ON COLUMN DWS.DWS_WATER_LEVEL_WARNING_LVL.ETL_TIME          IS '入仓时间';



-- 3. DWS_WATER_LEVEL_DAILY_AGG — 水位日级聚合（修正版）
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWS.DWS_WATER_LEVEL_DAILY_AGG (
      STAT_DATE, STATION_ID, STATION_CODE,
      AVG_LEVEL, P95_LEVEL, MAX_LEVEL, MIN_LEVEL,
      FIRST_OBS_TIME, LAST_OBS_TIME, ETL_TIME
    ) VALUES (
      TO_CHAR(SYSDATE - MOD(i, 60), 'YYYY-MM-DD'),
      TO_CHAR(i),
      'ST' || LPAD(i, 4, '0'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1, 10), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1, 10), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1, 10), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.1, 9), 3)),
      TO_CHAR(SYSDATE - MOD(i, 60), 'YYYY-MM-DD') || ' 00:00:00',
      TO_CHAR(SYSDATE - MOD(i, 60), 'YYYY-MM-DD') || ' 23:59:59',
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 4. DWS_WATER_LEVEL_HOURLY_AGG — 水位小时级聚合（修正版）
BEGIN
FOR i IN 1..500 LOOP
    INSERT INTO DWS.DWS_WATER_LEVEL_HOURLY_AGG (
      STATION_ID, STATION_CODE, HOUR_WINDOW,
      AVG_LEVEL, MIN_LEVEL, MAX_LEVEL, CNT,
      COMPUTE_MODE, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(i, 4, '0'),
      TO_CHAR(SYSDATE - MOD(i, 48)/24, 'YYYY-MM-DD HH24') || ':00:00',
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1, 10), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 5.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(5.0, 15.0), 3)),
      TO_CHAR(TRUNC(DBMS_RANDOM.VALUE(10, 100))),
      CASE WHEN MOD(i, 2) = 0 THEN 'batch' ELSE 'stream' END,
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/


-- ===========================================================
-- 1) ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND — 流域水位月报表（追加写）
-- ===========================================================
CREATE TABLE ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND (
                                                              BASIN_CODE   VARCHAR(4000) NULL,
                                                              STAT_MONTH   VARCHAR(4000) NULL,
                                                              STATION_CNT  VARCHAR(4000) NULL,
                                                              AVG_LEVEL    VARCHAR(4000) NULL,
                                                              MAX_LEVEL    VARCHAR(4000) NULL,
                                                              MIN_LEVEL    VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND IS '流域水位月报表（追加写）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND.BASIN_CODE  IS '流域编码（可空）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND.STAT_MONTH  IS '统计月份（YYYY-MM）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND.STATION_CNT IS '参与测站数';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND.AVG_LEVEL   IS '月平均水位';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND.MAX_LEVEL   IS '月最大水位';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND.MIN_LEVEL   IS '月最小水位';

-- ===========================================================
-- 2) ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL — 流域水位月报表（全量写）
-- ===========================================================
CREATE TABLE ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL (
                                                            BASIN_CODE   VARCHAR(4000) NULL,
                                                            STAT_MONTH   VARCHAR(4000) NULL,
                                                            STATION_CNT  VARCHAR(4000) NULL,
                                                            AVG_LEVEL    VARCHAR(4000) NULL,
                                                            MAX_LEVEL    VARCHAR(4000) NULL,
                                                            MIN_LEVEL    VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL IS '流域水位月报表（全量写）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL.BASIN_CODE  IS '流域编码（可空）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL.STAT_MONTH  IS '统计月份（YYYY-MM）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL.STATION_CNT IS '参与测站数';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL.AVG_LEVEL   IS '月平均水位';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL.MAX_LEVEL   IS '月最大水位';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL.MIN_LEVEL   IS '月最小水位';

-- ===========================================================
-- 3) ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE — 流域水位月报表（更新写）
-- ===========================================================
CREATE TABLE ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE (
                                                              BASIN_CODE   VARCHAR(4000) NULL,
                                                              STAT_MONTH   VARCHAR(4000) NULL,
                                                              STATION_CNT  VARCHAR(4000) NULL,
                                                              AVG_LEVEL    VARCHAR(4000) NULL,
                                                              MAX_LEVEL    VARCHAR(4000) NULL,
                                                              MIN_LEVEL    VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE IS '流域水位月报表（更新写）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE.BASIN_CODE  IS '流域编码（可空）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE.STAT_MONTH  IS '统计月份（YYYY-MM）';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE.STATION_CNT IS '参与测站数';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE.AVG_LEVEL   IS '月平均水位';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE.MAX_LEVEL   IS '月最大水位';
COMMENT ON COLUMN ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE.MIN_LEVEL   IS '月最小水位';

-- ===========================================================
-- 4) ADS_STATION_WATER_LEVEL_MONTH_REPORT — 站点水位预警月报表
-- ===========================================================
CREATE TABLE ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT (
                                                          STATION_ID      VARCHAR(4000) NULL,
                                                          STATION_NAME    VARCHAR(4000) NULL,
                                                          AVG_WATER_LEVEL VARCHAR(4000) NULL,
                                                          WARNING_LEVEL   VARCHAR(4000) NULL,
                                                          STAT_MONTH      VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT IS '站点水位预警月报表';
COMMENT ON COLUMN ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT.STATION_ID      IS '测站ID';
COMMENT ON COLUMN ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT.STATION_NAME    IS '测站名称';
COMMENT ON COLUMN ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT.AVG_WATER_LEVEL IS '月平均水位';
COMMENT ON COLUMN ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT.WARNING_LEVEL   IS '预警等级';
COMMENT ON COLUMN ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT.STAT_MONTH      IS '统计时间（YYYY-MM）';

-- ===========================================================
-- 5) ADS_WATER_LEVEL_DAILY_REPORT — 水位日统计报表
-- ===========================================================
CREATE TABLE ADS.ADS_WATER_LEVEL_DAILY_REPORT (
                                                  STAT_DATE         VARCHAR(4000) NULL,
                                                  STATION_CODE      VARCHAR(4000) NULL,
                                                  STATION_NAME      VARCHAR(4000) NULL,
                                                  AVG_LEVEL         VARCHAR(4000) NULL,
                                                  MAX_LEVEL         VARCHAR(4000) NULL,
                                                  MAX_TIME          VARCHAR(4000) NULL,
                                                  MIN_LEVEL         VARCHAR(4000) NULL,
                                                  MIN_TIME          VARCHAR(4000) NULL,
                                                  DATA_COMPLETENESS VARCHAR(4000) NULL,
                                                  WARNING_CNT       VARCHAR(4000) NULL,
                                                  ETL_TIME          VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_WATER_LEVEL_DAILY_REPORT IS '水位日统计报表';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.STAT_DATE         IS '统计日期';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.STATION_CODE      IS '站点编码';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.STATION_NAME      IS '站点名称';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.AVG_LEVEL         IS '日均';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.MAX_LEVEL         IS '日最大';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.MAX_TIME          IS '最大值出现时间';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.MIN_LEVEL         IS '日最小';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.MIN_TIME          IS '最小值出现时间';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.DATA_COMPLETENESS IS '当日采样完整率（%）';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.WARNING_CNT       IS '当日预警次数';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_DAILY_REPORT.ETL_TIME          IS '入仓时间';

-- ===========================================================
-- 6) ADS_WATER_LEVEL_HOURLY_REALTIME — 小时均值实时看板
-- ===========================================================
CREATE TABLE ADS.ADS_WATER_LEVEL_HOURLY_REALTIME (
                                                     STATION_CODE VARCHAR(4000) NULL,
                                                     HOUR_WINDOW  VARCHAR(4000) NULL,
                                                     AVG_LEVEL    VARCHAR(4000) NULL,
                                                     UPDATED_AT   VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_WATER_LEVEL_HOURLY_REALTIME IS '小时均值实时看板';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_HOURLY_REALTIME.STATION_CODE IS '站点编码';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_HOURLY_REALTIME.HOUR_WINDOW  IS '小时窗口起点';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_HOURLY_REALTIME.AVG_LEVEL    IS '小时均值';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_HOURLY_REALTIME.UPDATED_AT   IS '最近刷新时间';

-- ===========================================================
-- 7) ADS_WATER_LEVEL_WARNING_BOARD — 预警事件看板
-- ===========================================================
CREATE TABLE ADS.ADS_WATER_LEVEL_WARNING_BOARD (
                                                   EVENT_ID         VARCHAR(4000) NULL,
                                                   STATION_CODE     VARCHAR(4000) NULL,
                                                   WARNING_LEVEL    VARCHAR(4000) NULL,
                                                   START_TIME       VARCHAR(4000) NULL,
                                                   END_TIME         VARCHAR(4000) NULL,
                                                   DURATION_MINUTES VARCHAR(4000) NULL,
                                                   CURRENT_LEVEL    VARCHAR(4000) NULL,
                                                   STATUS           VARCHAR(4000) NULL,
                                                   ETL_TIME         VARCHAR(4000) NULL
);
COMMENT ON TABLE ADS.ADS_WATER_LEVEL_WARNING_BOARD IS '预警事件看板';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.EVENT_ID         IS '事件ID';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.STATION_CODE     IS '站点编码';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.WARNING_LEVEL    IS '3红/2黄/1蓝';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.START_TIME       IS '事件开始时间';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.END_TIME         IS '事件结束时间（可空）';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.DURATION_MINUTES IS '持续分钟数';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.CURRENT_LEVEL    IS '当前水位';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.STATUS           IS 'active/cleared';
COMMENT ON COLUMN ADS.ADS_WATER_LEVEL_WARNING_BOARD.ETL_TIME         IS '入仓时间';




-- ===========================================================
-- 1) ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND — 随机 200 条
-- ===========================================================
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_APPEND (
      BASIN_CODE, STAT_MONTH, STATION_CNT, AVG_LEVEL, MAX_LEVEL, MIN_LEVEL
    ) VALUES (
      'BASIN_' || LPAD(TO_CHAR(MOD(i,10)+1), 2, '0'),
      TO_CHAR(ADD_MONTHS(SYSDATE, -MOD(i,12)), 'YYYY-MM'),
      TO_CHAR(TRUNC(DBMS_RANDOM.VALUE(3, 50))),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 10.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1.0, 15.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.1, 5.0), 3))
    );
END LOOP;
END;
/
COMMIT;
/

-- ===========================================================
-- 2) ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL — 随机 200 条
-- ===========================================================
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_FULL (
      BASIN_CODE, STAT_MONTH, STATION_CNT, AVG_LEVEL, MAX_LEVEL, MIN_LEVEL
    ) VALUES (
      'BASIN_' || LPAD(TO_CHAR(MOD(i,10)+1), 2, '0'),
      TO_CHAR(ADD_MONTHS(SYSDATE, -MOD(i,12)), 'YYYY-MM'),
      TO_CHAR(TRUNC(DBMS_RANDOM.VALUE(3, 50))),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 10.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1.0, 15.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.1, 5.0), 3))
    );
END LOOP;
END;
/
COMMIT;
/

-- ===========================================================
-- 3) ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE — 随机 200 条
-- ===========================================================
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_BASIN_WATER_MONTH_REPORT_WIDE_UPDATE (
      BASIN_CODE, STAT_MONTH, STATION_CNT, AVG_LEVEL, MAX_LEVEL, MIN_LEVEL
    ) VALUES (
      'BASIN_' || LPAD(TO_CHAR(MOD(i,10)+1), 2, '0'),
      TO_CHAR(ADD_MONTHS(SYSDATE, -MOD(i,12)), 'YYYY-MM'),
      TO_CHAR(TRUNC(DBMS_RANDOM.VALUE(3, 50))),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 10.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1.0, 15.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.1, 5.0), 3))
    );
END LOOP;
END;
/
COMMIT;
/

-- ===========================================================
-- 4) ADS_STATION_WATER_LEVEL_MONTH_REPORT — 随机 200 条
-- ===========================================================
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_STATION_WATER_LEVEL_MONTH_REPORT (
      STATION_ID, STATION_NAME, AVG_WATER_LEVEL, WARNING_LEVEL, STAT_MONTH
    ) VALUES (
      TO_CHAR(i),
      '测站_' || TO_CHAR(i),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 12.0), 3)),
      CASE MOD(i,4) WHEN 0 THEN '无' WHEN 1 THEN '蓝' WHEN 2 THEN '黄' ELSE '红' END,
      TO_CHAR(ADD_MONTHS(SYSDATE, -MOD(i,12)), 'YYYY-MM')
    );
END LOOP;
END;
/
COMMIT;
/

-- ===========================================================
-- 5) ADS_WATER_LEVEL_DAILY_REPORT — 随机 200 条
--    时间串用拼接，避免无效掩码
-- ===========================================================
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_WATER_LEVEL_DAILY_REPORT (
      STAT_DATE, STATION_CODE, STATION_NAME,
      AVG_LEVEL, MAX_LEVEL, MAX_TIME,
      MIN_LEVEL, MIN_TIME, DATA_COMPLETENESS,
      WARNING_CNT, ETL_TIME
    ) VALUES (
      TO_CHAR(SYSDATE - MOD(i, 60), 'YYYY-MM-DD'),
      'ST' || LPAD(TO_CHAR(MOD(i,500)+1), 4, '0'),
      '测站_' || TO_CHAR(MOD(i,500)+1),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 10.0), 3)),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(1.0, 15.0), 3)),
      TO_CHAR(SYSDATE - MOD(i, 60), 'YYYY-MM-DD') || ' ' ||
        LPAD(TO_CHAR(MOD(i,24)), 2, '0') || ':' ||
        LPAD(TO_CHAR(MOD(i,60)), 2, '0') || ':' ||
        LPAD(TO_CHAR(MOD(i*7,60)), 2, '0'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.1, 5.0), 3)),
      TO_CHAR(SYSDATE - MOD(i, 60), 'YYYY-MM-DD') || ' ' ||
        LPAD(TO_CHAR(MOD(i*3,24)), 2, '0') || ':' ||
        LPAD(TO_CHAR(MOD(i*5,60)), 2, '0') || ':' ||
        LPAD(TO_CHAR(MOD(i*11,60)), 2, '0'),
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(70, 100), 2)),  -- 百分比
      TO_CHAR(TRUNC(DBMS_RANDOM.VALUE(0, 6))),        -- 当日预警次数
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- ===========================================================
-- 6) ADS_WATER_LEVEL_HOURLY_REALTIME — 随机 200 条
--    HOUR_WINDOW 以整点形式输出
-- ===========================================================
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_WATER_LEVEL_HOURLY_REALTIME (
      STATION_CODE, HOUR_WINDOW, AVG_LEVEL, UPDATED_AT
    ) VALUES (
      'ST' || LPAD(TO_CHAR(MOD(i,500)+1), 4, '0'),
      TO_CHAR(SYSDATE - (i/24), 'YYYY-MM-DD HH24') || ':00:00',
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 10.0), 3)),
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
/
COMMIT;
/

-- 7) ADS_WATER_LEVEL_WARNING_BOARD — 随机 200 条（无 /，无 DECLARE）
BEGIN
FOR i IN 1..200 LOOP
    INSERT INTO ADS.ADS_WATER_LEVEL_WARNING_BOARD (
      EVENT_ID, STATION_CODE, WARNING_LEVEL, START_TIME, END_TIME,
      DURATION_MINUTES, CURRENT_LEVEL, STATUS, ETL_TIME
    ) VALUES (
      TO_CHAR(i),
      'ST' || LPAD(TO_CHAR(MOD(i,500)+1), 4, '0'),
      TO_CHAR(MOD(i,3)+1),  -- 1/2/3
      TO_CHAR(SYSDATE - (MOD(i,10)/24), 'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(SYSDATE - (MOD(i,10)/24) + (MOD(i,180)/1440), 'YYYY-MM-DD HH24:MI:SS'),
      TO_CHAR(MOD(i,180)),  -- 0~179
      TO_CHAR(ROUND(DBMS_RANDOM.VALUE(0.5, 15.0), 3)),
      CASE WHEN MOD(i,2)=0 THEN 'active' ELSE 'cleared' END,
      TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS')
    );
END LOOP;
END;
COMMIT;
