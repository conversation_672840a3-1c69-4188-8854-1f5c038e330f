[mysqld]
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
datadir         = /var/lib/mysql
#log-error      = /var/log/mysql/error.log
#bind-address   = 127.0.0.1
symbolic-links=0

character_set_server=utf8mb4
character_set_filesystem=utf8mb4
collation-server=utf8mb4_unicode_ci
init-connect='SET NAMES utf8mb4'
init_connect='SET collation_connection = utf8mb4_unicode_ci'
skip-character-set-client-handshake
lower_case_table_names=1

# 最大连接数
max_connections=500000
# 连接超时时间
connect_timeout=30
# 最大包大小 (16MB)
max_allowed_packet=16777216

# =========================
# 开启二进制日志 (binlog)
# =========================
server-id = 123
log_bin = mysql-bin
binlog_format = row
binlog_row_image = full
expire_logs_days = 10
gtid_mode = on
enforce_gtid_consistency = on
