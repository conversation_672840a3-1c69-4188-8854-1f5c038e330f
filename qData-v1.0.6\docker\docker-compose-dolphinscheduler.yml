version: "1.0.5"

services:
  dolphinscheduler-postgresql:
    image: bitnami/postgresql:15.2.0
#    ports:
#      - "5432:5432"
    profiles: ["schema", "all", "local", "dsdb" ]
    environment:
      POSTGRESQL_USERNAME: ${SPRING_DATASOURCE_USERNAME}
      POSTGRESQL_PASSWORD: ${SPRING_DATASOURCE_PASSWORD}
      POSTGRESQL_DATABASE: ${POSTGRESQL_DATABASE}
    volumes:
#      - dolphinscheduler-postgresql:/bitnami/postgresql
      - ./dolphinscheduler/sql/dolphinscheduler_dump.sql:/docker-entrypoint-initdb.d/01_dolphinscheduler.sql:ro
    healthcheck:
      test: ["CMD", "bash", "-c", "cat < /dev/null > /dev/tcp/127.0.0.1/5432"]
      interval: 5s
      timeout: 60s
      retries: 120
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"

  dolphinscheduler-zookeeper:
    image: bitnami/zookeeper:3.7.1
    profiles: ["all", "local" ]
    environment:
      ALLOW_ANONYMOUS_LOGIN: "yes"
      ZOO_4LW_COMMANDS_WHITELIST: srvr,ruok,wchs,cons
      ZOO_LOG_LEVEL: ERROR
    volumes:
      - dolphinscheduler-zookeeper:/bitnami/zookeeper
    healthcheck:
      test: ["CMD", "bash", "-c", "cat < /dev/null > /dev/tcp/127.0.0.1/2181"]
      interval: 5s
      timeout: 60s
      retries: 120
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"

#  dolphinscheduler-schema-initializer:
#    image: ${HUB}/dolphinscheduler-tools:${TAG}
#    env_file: .env
#    profiles: ["schema"]
#    command: [ tools/bin/upgrade-schema.sh ]
#    depends_on:
#      dolphinscheduler-postgresql:
#        condition: service_healthy
#    volumes:
#      - ./dolphinscheduler/logs:/opt/dolphinscheduler/logs
#      - ./dolphinscheduler/soft:/opt/soft
#      - ./dolphinscheduler/resource:/dolphinscheduler
#    networks:
#      - qdatanet
#    extra_hosts:
#      - "host.docker.internal:host-gateway"

  dolphinscheduler-api:
    image: ${HUB}/dolphinscheduler-api:${TAG}
    ports:
      - "12345:12345"
      - "25333:25333"
    profiles: ["all", "local" ]
    env_file: .env
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:12345/dolphinscheduler/actuator/health" ]
      interval: 30s
      timeout: 5s
      retries: 3
    depends_on:
      dolphinscheduler-zookeeper:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./dolphinscheduler/logs:/opt/dolphinscheduler/logs
      - ./dolphinscheduler/soft:/opt/soft
      - ./dolphinscheduler/resource:/dolphinscheduler
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"

  dolphinscheduler-alert:
    image: ${HUB}/dolphinscheduler-alert-server:${TAG}
    profiles: ["all", "local" ]
    env_file: .env
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:50053/actuator/health" ]
      interval: 30s
      timeout: 5s
      retries: 3
    depends_on:
      dolphinscheduler-zookeeper:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./dolphinscheduler/logs:/opt/dolphinscheduler/logs
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"

  dolphinscheduler-master:
    image: ${HUB}/dolphinscheduler-master:${TAG}
    profiles: ["all", "local" ]
    env_file: .env
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:5679/actuator/health" ]
      interval: 30s
      timeout: 5s
      retries: 3
    depends_on:
      dolphinscheduler-zookeeper:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./dolphinscheduler/logs:/opt/dolphinscheduler/logs
      - ./dolphinscheduler/soft:/opt/soft
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"

  dolphinscheduler-worker:
    image: ${HUB}/dolphinscheduler-worker:${TAG}
    profiles: ["all", "local" ]
    env_file: .env
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:1235/actuator/health" ]
      interval: 30s
      timeout: 5s
      retries: 3
    depends_on:
      dolphinscheduler-zookeeper:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - dolphinscheduler-worker-data:/tmp/dolphinscheduler
      - ./dolphinscheduler/logs:/opt/dolphinscheduler/logs
      - ./dolphinscheduler/soft:/opt/soft
      - ./dolphinscheduler/resource:/dolphinscheduler
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"
