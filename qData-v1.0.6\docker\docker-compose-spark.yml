version: "1.0.5"

services:
  spark:
    image: docker.io/bitnami/spark:3
    profiles: ["all", "local" ]
    environment:
      - SPARK_MASTER_HOST=spark         # 关键：让 master 对外广播服务名
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
#    ports:
#      - '8080:8080'
#      - '4040:4040'
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080" ]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 5s
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"

  spark-worker-1:
    profiles: ["all", "local" ]
    image: docker.io/bitnami/spark:3
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=${SPARK_MASTER_URL}
      - SPARK_WORKER_MEMORY=${SPARK_WORKER_MEMORY}
      - SPARK_WORKER_CORES=${SPARK_WORKER_CORES}
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
#    ports:
#      - '8081:8081'
    depends_on:
      spark:
        condition: service_healthy
    networks:
      - qdatanet
    extra_hosts:
      - "host.docker.internal:host-gateway"
