server {
    listen 80;
    server_name localhost;

    client_max_body_size 500M;
    add_header Strict-Transport-Security "max-age=31536000";
    proxy_connect_timeout 600s;
    proxy_send_timeout 600s;
    proxy_read_timeout 600s;
    keepalive_timeout 600s;
    send_timeout 60s;

    location / {
        proxy_read_timeout 600;
        charset utf-8;
        root   /usr/share/nginx;
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
    }

    location /prod-api {
        add_header 'Access-Control-Allow-Methods' 'GET, POST,PUT,DELETE, OPTIONS';
        proxy_read_timeout 6000;
        rewrite ^/prod-api/(.*)$ /$1 break;
        proxy_set_header X-Real-Ip $remote_addr;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_pass http://qdata-api:8080;
    }

}

