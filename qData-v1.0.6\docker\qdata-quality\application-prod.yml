# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10
    #万能密码 配置为空则不生效
    universalPassword: qData123

# 主数据源选择
datasource:
  type: dm8

# Spring配置
spring:
  # redis 配置
  redis:
    # 地址
    host: redis
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # 密码
    password: J98%FHF#9h@e88h9fre9
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  rabbitmq:
    host: rabbitmq
    port: 5672
    username: admin
    password: Ej^iUNFLp9MQouc1
#    listener:
#      direct:
#        auto-startup: false
#      simple:
#        auto-startup: false
#        acknowledge-mode: manual
#        concurrency: 1
#        max-concurrency: 10
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 1
        max-concurrency: 10
  data:
    mongodb:
      uri: *******************************************************************
      field-naming-strategy: org.springframework.data.mapping.model.SnakeCaseFieldNamingStrategy # 自动转驼峰
      print: true
      slowQuery: true
      slowTime: 1000
  datasource:
    druid:
      stat-view-servlet:
        # 是否启用Druid的监控统计功能
        enabled: true
        # 访问Druid监控页面的用户名
        loginUsername: qdata
        # 访问Druid监控页面的密码
        loginPassword: 123456
    dynamic:
      druid:
        # 连接池初始化时创建的连接数量
        initial-size: 5
        # 连接池中最小空闲连接数
        min-idle: 5
        # 连接池中最大活动连接数
        maxActive: 20
        # 连接池等待连接的最长时间（毫秒）
        maxWait: 60000
        # 数据库连接超时时间（毫秒）
        connectTimeout: 30000
        # Socket超时时间（毫秒）
        socketTimeout: 60000
        # 空闲连接的检测周期（毫秒）
        timeBetweenEvictionRunsMillis: 60000
        # 最小空闲连接的存活时间（毫秒）
        minEvictableIdleTimeMillis: 300000
        # 用于检测连接是否有效的SQL语句
        validationQuery: SELECT 1 FROM DUAL
        # 是否在空闲时检测连接的有效性
        testWhileIdle: true
        # 借用连接时是否测试连接的有效性
        testOnBorrow: false
        # 归还连接时是否测试连接的有效性
        testOnReturn: false
        # 是否打开连接池的PreparedStatement缓存
        poolPreparedStatements: true
        # 每个连接池的PreparedStatement缓存上限
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置Druid的过滤器
        filters: stat,slf4j
        # Druid连接属性配置
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源配置
        master:
          # 动态加载的配置属性
          driver-class-name: ${${datasource.type}.driver-class-name}
          url: ${${datasource.type}.url}
          username: ${${datasource.type}.username}
          password: ${${datasource.type}.password}

# MySQL配置文件
mysql:
  # JDBC驱动类名
  driver-class-name: com.mysql.cj.jdbc.Driver
  # 主库JDBC连接URL
  url: *********************************************************************************************************************************************************
  # 主库用户名
  username: root
  # 主库密码
  password: a2g5K3YW

# 达梦配置文件
dm8:
  # JDBC驱动类名
  driver-class-name: dm.jdbc.driver.DmDriver
  # 主库JDBC连接URL
  url: jdbc:dm://dm8:5236/QDATA?STU&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&schema=QDATA&serverTimezone=Asia/Shanghai
  # 主库用户名
  username: QDATA
  # 主库密码
  password: s2LKr6LMQxVDTQx

# 人大金仓配置文件
kingbase8:
  # JDBC驱动类名
  driver-class-name: com.kingbase8.Driver
  # 主库JDBC连接URL
  url: ******************************************************************************************************************************************************************************************************
  # 主库用户名
  username: kingbase
  # 主库密码
  password: 123456

# Oracle12c 配置文件
oracle:
  # JDBC驱动类名
  driver-class-name: oracle.jdbc.OracleDriver
  # 主库JDBC连接URL
  url: **************************************************************************************************************************************
  # 主库用户名
  username: ANIVIA
  # 主库密码
  password: ANIVIA


##文件存储路径
file:
  job:
    log:
      qualitytask_prefix_url: /usr/app/jar/job-log
