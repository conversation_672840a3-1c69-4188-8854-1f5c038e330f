{"rabbit_version": "3.12.13", "rabbitmq_version": "3.12.13", "product_name": "RabbitMQ", "product_version": "3.12.13", "users": [{"name": "admin", "password_hash": "4EV8ba3AnxA3tnWXvTkTVgRJS9TlyTWiBi4JTmNVg7ktz7Kl", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": ["administrator"], "limits": {}}], "vhosts": [{"name": "/"}], "permissions": [{"user": "admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "internal_cluster_id", "value": "rabbitmq-cluster-id-OAb2DJSJD_dkBesR2Xa4kg"}], "policies": [], "queues": [{"name": "ds.queue.processInstance", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "ds.queue.taskInstance.insert", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "ds.queue.taskInstance.update", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "ds.queue.taskInstance.log", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [{"name": "ds.exchange.taskInstance.log", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ds.exchange.processInstance", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ds.exchange.taskInstance", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "ds.exchange.processInstance", "vhost": "/", "destination": "ds.queue.processInstance", "destination_type": "queue", "routing_key": "ds.queue.processInstance", "arguments": {}}, {"source": "ds.exchange.taskInstance", "vhost": "/", "destination": "ds.queue.taskInstance.insert", "destination_type": "queue", "routing_key": "ds.queue.taskInstance.insert", "arguments": {}}, {"source": "ds.exchange.taskInstance", "vhost": "/", "destination": "ds.queue.taskInstance.update", "destination_type": "queue", "routing_key": "ds.queue.taskInstance.update", "arguments": {}}, {"source": "ds.exchange.taskInstance.log", "vhost": "/", "destination": "ds.queue.taskInstance.log", "destination_type": "queue", "routing_key": "ds.queue.taskInstance.log", "arguments": {}}]}