{
  "config": {
    "resourceUrl": "静态资源前缀",
    "rabbitmq": {
      "host": "127.0.0.1",
      "port": 5672,
      "username": "admin",
      "password": "密码"
    },
    "taskInfo": {
      "projectCode":"项目编码",
      "taskCode": "1",//任务编码
      "taskVersion": 1,//任务版本
      "name":"任务名称"
    }
  },
  "reader": {
    "projectCode": "项目编码",
    "nodeCode": "节点编码",
    "nodeVersion": 1,//节点版本
    "componentType": "类型 1:数据库输入 2:EXCEL输入 3:kafka输入 4:CSV输入",
    "parameter": {
      "bootstrapServers": "",//连接信息
      "topic": "test-kafka",//主题
      "column": [
        {
          "name": "id",
          "type": "LONG",
          "key": "info.id"
        },
        {
          "name": "t1",
          "type": "STRING",
          "key": "info.t1"
        },
        {
          "name": "t2",
          "type": "BOOL",
          "key": "info.t2"
        },
        {
          "name": "t3",
          "type": "BYTES",
          "key": "info.t3"
        },
        {
          "name": "t4",
          "type": "DATE",
          "key": "info.t4"
        },
        {
          "name": "t5",
          "type": "DOUBLE",
          "key": "info.t5"
        }
      ],
      "config": {//配置
        "group.id": "test1"
      }
    }
  },
  "transition": [],
  "writer": {
  }
}
