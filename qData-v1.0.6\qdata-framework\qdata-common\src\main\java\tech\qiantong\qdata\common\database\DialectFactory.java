package tech.qiantong.qdata.common.database;

import tech.qiantong.qdata.common.database.constants.DbType;
import tech.qiantong.qdata.common.database.dialect.DialectRegistry;

/**
 * 方言工厂类
 *
 * <AUTHOR>
 * @date 2022-11-14
 */
public class DialectFactory {

    private static final DialectRegistry DIALECT_REGISTRY = new DialectRegistry();

    public static DbDialect getDialect(DbType dbType) {
        return DIALECT_REGISTRY.getDialect(dbType);
    }
}
