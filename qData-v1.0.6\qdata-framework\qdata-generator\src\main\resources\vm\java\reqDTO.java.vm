package ${packageName}.api.${moduleName}.dto;

#foreach ($import in $importList)
import ${import};
#end
import lombok.*;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName} DTO 对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
public class ${ClassName}ReqDTO {

    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField) && $column.javaField != "creatorId" && $column.javaField != "updatorId")
    /** $column.columnComment */
    #if($column.javaField == "id")
    private $column.javaType $column.javaField;

    #elseif($column.javaField == "delFlag")
    private $column.javaType $column.javaField;

    #else
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#end
    private $column.javaType $column.javaField;

    #end
#end
#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    private List<${subClassName}> ${subclassName}List;

#end

#if($table.sub)
    public List<${subClassName}> get${subClassName}List()
    {
        return ${subclassName}List;
    }

    public void set${subClassName}List(List<${subClassName}> ${subclassName}List)
    {
        this.${subclassName}List = ${subclassName}List;
    }

#end
}
