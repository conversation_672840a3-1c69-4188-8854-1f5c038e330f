package ${packageName}.service.${moduleName};

import java.util.List;
import java.util.Map;
import java.util.Collection;
import com.baomidou.mybatisplus.extension.service.IService;
import tech.qiantong.qdata.common.core.page.PageResult;
import ${packageName}.controller.admin.${moduleName}.vo.${ClassName}SaveReqVO;
import ${packageName}.controller.admin.${moduleName}.vo.${ClassName}PageReqVO;
import ${packageName}.dal.dataobject.${moduleName}.${ClassName}DO;
#if($table.sub)
#elseif($table.tree)
#end
/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IService<${ClassName}DO> {

    /**
     * 获得${functionName}分页列表
     *
     * @param pageReqVO 分页请求
     * @return ${functionName}分页列表
     */
    PageResult<${ClassName}DO> get${ClassName}Page(${ClassName}PageReqVO pageReqVO);

    /**
     * 创建${functionName}
     *
     * @param createReqVO ${functionName}信息
     * @return ${functionName}编号
     */
    Long create${ClassName}(${ClassName}SaveReqVO createReqVO);

    /**
     * 更新${functionName}
     *
     * @param updateReqVO ${functionName}信息
     */
    int update${ClassName}(${ClassName}SaveReqVO updateReqVO);

    #if($table.crud || $table.sub)
    /**
     * 删除${functionName}
     *
     * @param idList ${functionName}编号
     */
    int remove${ClassName}(Collection<Long> idList);
    #elseif($table.tree)
    /**
     * 删除${functionName}
     *
     * @param id ${functionName}编号
     */
    int remove${ClassName}(Long id);
    #end

    /**
     * 获得${functionName}详情
     *
     * @param id ${functionName}编号
     * @return ${functionName}
     */
    ${ClassName}DO get${ClassName}ById(Long id);

    /**
     * 获得全部${functionName}列表
     *
     * @return ${functionName}列表
     */
    List<${ClassName}DO> get${ClassName}List();

    /**
     * 获得全部${functionName} Map
     *
     * @return ${functionName} Map
     */
    Map<Long, ${ClassName}DO> get${ClassName}Map();

    #if($table.tree)
    /**
     * 是否存在${functionName}子节点
     *
     * @param id ${functionName}id
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildBy${ClassName}Id(Long id);
    #end

    #if($table.crud || $table.sub)
    /**
     * 导入${functionName}数据
     *
     * @param importExcelList ${functionName}数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String import${ClassName}(List<${ClassName}RespVO> importExcelList, boolean isUpdateSupport, String operName);
    #end

}
