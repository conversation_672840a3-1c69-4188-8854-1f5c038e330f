<!-- 复杂详情路由模板
    {
        path: '/${controllerPrefix}/${moduleName}',
        component: Layout,
        redirect: '${moduleName}',
        hidden: true,
        children: [
            {
                path: '${businessName}Detail',
                component: () => import('@/views/${controllerPrefix}/${moduleName}/detail/index.vue'),
                name: 'tree',
                meta: { title: '${functionName}详情', activeMenu: '/${topModule}/${businessName}'  }
            }
        ]
    }
 -->



<template>
  <div class="app-container" ref="app-container">
    <div class="pagecont-top" v-show="showSearch" style="padding-bottom:15px">
      <div class="infotop" >
        <div class="infotop-title mb15">
            #foreach($column in $columns)
              #set($javaField=$column.javaField)
              #if($column.pk)
              {{ ${businessName}Detail.${javaField} }}
            #end
            #end
        </div>
        <el-row :gutter="20">
          #foreach($column in $columns)
            #set($javaField=$column.javaField)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
              #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
              #set($comment=$column.columnComment)
            #end
          #if($column.pk)
            <el-col :span="8">
              <div class="infotop-row border-top">
                <div class="infotop-row-lable">${comment}</div>
                <div class="infotop-row-value">{{ ${businessName}Detail.${javaField} }}</div>
              </div>
            </el-col>
          #elseif($column.list && $column.htmlType == "datetime" )
            <el-col :span="8">
              <div class="infotop-row border-top">
                <div class="infotop-row-lable">${comment}</div>
                <div class="infotop-row-value">{{ parseTime(${businessName}Detail.${javaField}, '{y}-{m}-{d}') }}</div>
              </div>
            </el-col>
          #elseif($column.list && $column.htmlType == "imageUpload")
            <el-col :span="8">
              <div class="infotop-row border-top">
                <div class="infotop-row-lable">${comment}</div>
                <div class="infotop-row-value">
                  <image-preview :src="${businessName}Detail.${javaField}" :width="50" :height="50"/>
                </div>
              </div>
            </el-col>
          #elseif($column.list && "" != $column.dictType)
            <el-col :span="8">
              <div class="infotop-row border-top">
                <div class="infotop-row-lable">${comment}</div>
                <div class="infotop-row-value">
                    <dict-tag :options="${column.dictType}" :value="${businessName}Detail.${javaField} "/>
                </div>
              </div>
            </el-col>
          #elseif($column.list && "" != $javaField)
            <el-col :span="8">
              <div class="infotop-row border-top">
                <div class="infotop-row-lable">${comment}</div>
                <div class="infotop-row-value">
                  {{ ${businessName}Detail.${javaField} || '-' }}
                </div>
              </div>
            </el-col>
          #end
          #end
        </el-row>

      </div>
    </div>

    <div  class="pagecont-bottom">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="组件一" name="1">
          <component-one ></component-one>
        </el-tab-pane>
        <el-tab-pane label="组件二" name="2">
          <component-two ></component-two>
        </el-tab-pane>
      </el-tabs>
    </div>


  </div>
</template>

<script setup name="${BusinessName}">
import {get${BusinessName}} from "@/api/";
import {useRoute} from 'vue-router';

const { proxy } = getCurrentInstance();
    #if(${dicts} != '')
      #set($dictsNoSymbol=$dicts.replace("'", ""))
    const { ${dictsNoSymbol} } = proxy.useDict(${dicts});
    #end

  const activeName = ref('1')

  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const showSearch = ref(true);
  const route = useRoute();
  let id = route.query.id || 1;
  // 监听 id 变化
  watch(
          () => route.query.id,
          (newId) => {
            id = newId || 1;  // 如果 id 为空，使用默认值 1
            get${BusinessName}DetailById();

          },
          { immediate: true }  // `immediate` 为 true 表示页面加载时也会立即执行一次 watch
  );
  const data = reactive({
      ${businessName}Detail: {
    },
    form: {},
  });

  const {  ${businessName}Detail, rules } = toRefs(data);

  /** 复杂详情页面上方表单查询 */
  function get${BusinessName}DetailById() {
    ## const _${pkColumn.javaField} = row.${pkColumn.javaField} || ids.value
    const _${pkColumn.javaField} = id ;
    get${BusinessName}(_${pkColumn.javaField}).then(response => {
        ${businessName}Detail.value = response.data;
      #foreach ($column in $columns)
        #if($column.htmlType == "checkbox")
            ${businessName}Detail.value.$column.javaField = ${businessName}Detail.value.${column.javaField}.split(",");
        #end
      #end
      #if($table.sub)
          ${subclassName}List.value = response.data.${subclassName}List;
      #end
    });
  }

  get${BusinessName}DetailById();

</script>
