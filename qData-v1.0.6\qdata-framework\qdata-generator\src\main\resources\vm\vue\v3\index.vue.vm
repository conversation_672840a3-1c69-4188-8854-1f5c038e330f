<template>
  <div class="app-container" ref="app-container">
    <div class="pagecont-top" v-show="showSearch">
     <el-form class="btn-style" :model="queryParams" ref="queryRef" :inline="true" label-width="75px" v-show="showSearch" @submit.prevent>
        #foreach($column in $columns)
            #if($column.query)
                #set($dictType=$column.dictType)
                #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
                #if($column.htmlType == "input")
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-input
            class="el-form-input-width"
            v-model="queryParams.${column.javaField}"
            placeholder="请输入${comment}"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-select class="el-form-input-width" v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable>
          <el-option
              v-for="dict in ${dictType}"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
      <el-form-item class="el-form-input-width" label="${comment}" prop="${column.javaField}">
        <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" clearable>
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
#elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
      <el-form-item label="${comment}" prop="${column.javaField}">
        <el-date-picker class="el-form-input-width"
            clearable
            v-model="queryParams.${column.javaField}"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择${comment}">
        </el-date-picker>
      </el-form-item>
#elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
      <el-form-item label="${comment}" >
        <el-date-picker
            class="el-form-input-width"
            v-model="daterange${AttrName}"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
        #end
    #end
#end

      <el-form-item>
        <el-button plain type="primary" @click="handleQuery" @mousedown="(e) => e.preventDefault()">
          <i class="iconfont-mini icon-a-zu22377 mr5"></i>查询
        </el-button>
        <el-button @click="resetQuery" @mousedown="(e) => e.preventDefault()">
          <i class="iconfont-mini icon-a-zu22378 mr5"></i>重置
        </el-button>
      </el-form-item>
     </el-form>
    </div>

    <div  class="pagecont-bottom">
     <div class="justify-between mb15">
       <el-row :gutter="15" class="btn-style">
         <el-col :span="1.5">
           <el-button type="primary" plain @click="handleAdd" v-hasPermi="['${permissionPrefix}:add']"
                      @mousedown="(e) => e.preventDefault()">
             <i class="iconfont-mini icon-xinzeng mr5"></i>新增
           </el-button>
         </el-col>
         <el-col :span="1.5">
           <el-button type="primary" plain :disabled="single" @click="handleUpdate" v-hasPermi="['${permissionPrefix}:edit']"
                      @mousedown="(e) => e.preventDefault()">
             <i class="iconfont-mini icon-xiugai--copy mr5"></i>修改
           </el-button>
         </el-col>
         <el-col :span="1.5">
           <el-button type="danger" plain :disabled="multiple" @click="handleDelete" v-hasPermi="['${permissionPrefix}:remove']"
                      @mousedown="(e) => e.preventDefault()">
             <i class="iconfont-mini icon-shanchu-huise mr5"></i>删除
           </el-button>
         </el-col>
         <el-col :span="1.5">
           <el-button type="info" plain  @click="handleImport" v-hasPermi="['${permissionPrefix}:export']"
                      @mousedown="(e) => e.preventDefault()">
             <i class="iconfont-mini icon-upload-cloud-line mr5"></i>导入
           </el-button>
         </el-col>
         <el-col :span="1.5">
           <el-button type="warning" plain @click="handleExport" v-hasPermi="['${permissionPrefix}:export']"
                      @mousedown="(e) => e.preventDefault()">
             <i class="iconfont-mini icon-download-line mr5"></i>导出
           </el-button>
         </el-col>
       </el-row>
       <div class="justify-end top-right-btn">
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
       </div>
     </div>
     <el-table stripe height="60vh" v-loading="loading" :data="${businessName}List" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
       <el-table-column type="selection" width="55" align="center" />
         #foreach($column in $columns)
             #set($javaField=$column.javaField)
             #set($parentheseIndex=$column.columnComment.indexOf("（"))
             #if($parentheseIndex != -1)
                 #set($comment=$column.columnComment.substring(0, $parentheseIndex))
             #else
                 #set($comment=$column.columnComment)
             #end
             #if($column.pk)
       <el-table-column v-if="getColumnVisibility($foreach.index)" label="${comment}" align="center" prop="${javaField}" />
             #elseif($column.list && $column.htmlType == "datetime" && $column.columnName != "create_time")
       <el-table-column v-if="getColumnVisibility($foreach.index)" label="${comment}" align="center" prop="${javaField}" width="180">
         <template #default="scope">
           <span>{{ parseTime(scope.row.${javaField}, '{y}-{m}-{d}') }}</span>
         </template>
       </el-table-column>
             #elseif($column.list && $column.htmlType == "datetime" && $column.columnName == "create_time")
       <el-table-column v-if="getColumnVisibility($foreach.index)" label="${comment}" align="center" prop="${javaField}" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
         <template #default="scope">
           <span>{{ parseTime(scope.row.${javaField}, '{y}-{m}-{d}') }}</span>
         </template>
       </el-table-column>
             #elseif($column.list && $column.htmlType == "imageUpload")
       <el-table-column v-if="getColumnVisibility($foreach.index)" label="${comment}" align="center" prop="${javaField}" width="100">
         <template #default="scope">
           <image-preview :src="scope.row.${javaField}" :width="50" :height="50"/>
         </template>
       </el-table-column>
             #elseif($column.list && "" != $column.dictType)
       <el-table-column v-if="getColumnVisibility($foreach.index)" label="${comment}" align="center" prop="${javaField}">
         <template #default="scope">
             #if($column.htmlType == "checkbox")
               <dict-tag :options="${column.dictType}" :value="scope.row.${javaField} ? scope.row.${javaField}.split(',') : []"/>
             #else
               <dict-tag :options="${column.dictType}" :value="scope.row.${javaField}"/>
             #end
         </template>
       </el-table-column>
             #elseif($column.list && "" != $javaField)
       <el-table-column v-if="getColumnVisibility($foreach.index)" label="${comment}" align="center" prop="${javaField}">
         <template #default="scope">
           {{ scope.row.${javaField} || '-' }}
         </template>
       </el-table-column>
             #end
         #end
       <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="240">
         <template #default="scope">
           <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                      v-hasPermi="['${permissionPrefix}:edit']">修改</el-button>
           <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                      v-hasPermi="['${permissionPrefix}:remove']">删除</el-button>
           <el-button link type="primary" icon="view" @click="handleDetail(scope.row)"
                      v-hasPermi="['${permissionPrefix}:edit']">详情</el-button>
           <el-button link type="primary" icon="view" @click="routeTo('/${controllerPrefix}/${moduleName}/${businessName}Detail',scope.row)"
                      v-hasPermi="['${permissionPrefix}:edit']">复杂详情</el-button>
         </template>
       </el-table-column>

       <template #empty>
         <div class="emptyBg">
           <img src="@/assets/system/images/no_data/noData.png" alt="" />
           <p>暂无记录</p>
         </div>
       </template>
     </el-table>

     <pagination
         v-show="total>0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
     />
    </div>

    <!-- 添加或修改${functionName}对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" :append-to="$refs['app-container']" draggable>
      <template #header="{ close, titleId, titleClass }">
        <span role="heading" aria-level="2" class="el-dialog__title">
          {{ title }}
        </span>
      </template>
      <el-form ref="${businessName}Ref" :model="form" :rules="rules" label-width="80px" @submit.prevent>
          #set($counter = 0)
          #foreach($column in $columns)
              #set($field=$column.javaField)
              #if($column.edit && !$column.pk)
                  #if(($column.usableColumn) || (!$column.superColumn))
                      #set($parentheseIndex=$column.columnComment.indexOf("（"))
                      #if($parentheseIndex != -1)
                          #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                      #else
                          #set($comment=$column.columnComment)
                      #end
                      #set($dictType=$column.dictType)
                      #if($counter % 2 == 0)
          <el-row :gutter="20">
#end
#if($column.htmlType == "input")
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-input v-model="form.${field}" placeholder="请输入${comment}" />
              </el-form-item>
            </el-col>
                      #elseif($column.htmlType == "select" && "" != $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-select v-model="form.${field}" placeholder="请选择${comment}">
                  <el-option
                      v-for="dict in ${dictType}"
                      :key="dict.value"
                      :label="dict.label"
                      #if($column.javaType == "Integer" || $column.javaType == "Long")
                      :value="parseInt(dict.value)"
                      #else
                      :value="dict.value"
                      #end
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "select" && $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-select v-model="form.${field}" placeholder="请选择${comment}">
                  <el-option label="请选择字典生成" value="" />
                </el-select>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "checkbox" && "" != $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-checkbox-group v-model="form.${field}">
                  <el-checkbox
                      v-for="dict in ${dictType}"
                      :key="dict.value"
                      :label="dict.value">
                    {{dict.label}}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "checkbox" && $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-checkbox-group v-model="form.${field}">
                  <el-checkbox>请选择字典生成</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "radio" && "" != $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-radio-group v-model="form.${field}">
                  <el-radio
                      v-for="dict in ${dictType}"
                      :key="dict.value"
                      #if($column.javaType == "Integer" || $column.javaType == "Long")
                      :label="parseInt(dict.value)"
                      #else
                      :label="dict.value"
                      #end
                  >{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "radio" && $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-radio-group v-model="form.${field}">
                  <el-radio label="1">请选择字典生成</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "datetime")
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-date-picker clearable
                                style="width: 100%"
                                v-model="form.${field}"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="请选择${comment}">
                </el-date-picker>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "imageUpload")
            <el-col :span="24">
              <el-form-item label="${comment}" prop="${field}">
                <image-upload v-model="form.${field}" />
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "fileUpload")
            <el-col :span="24">
              <el-form-item label="${comment}" prop="${field}">
                <file-upload v-model="form.${field}" />
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "editor")
            <el-col :span="24">
              <el-form-item label="${comment}">
                <editor v-model="form.${field}" :min-height="192"/>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "textarea")
            <el-col :span="24">
              <el-form-item label="${comment}" prop="${field}">
                <el-input v-model="form.${field}" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
#end
#set($counter = $counter + 1)
#if($counter % 2 == 0)
          </el-row>
          #end
      #end
  #end
#end
          #if($counter % 2 != 0)
            </el-row>
          #end
          #if($table.sub)
            <el-divider content-position="center">${subTable.functionName}信息</el-divider>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" icon="Plus" @click="handleAdd${subClassName}">添加</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" icon="Delete" @click="handleDelete${subClassName}">删除</el-button>
              </el-col>
            </el-row>
            <el-table :data="${subclassName}List" :row-class-name="row${subClassName}Index" @selection-change="handle${subClassName}SelectionChange" ref="${subclassName}">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="序号" align="center" prop="index" width="50"/>
                #foreach($column in $subTable.columns)
                    #set($javaField=$column.javaField)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if($column.pk || $javaField == ${subTableFkclassName})
                    #elseif($column.list && $column.htmlType == "input")
                      <el-table-column label="$comment" prop="${javaField}" width="150">
                        <template #default="scope">
                          <el-input v-model="scope.row.$javaField" placeholder="请输入$comment" />
                        </template>
                      </el-table-column>
                    #elseif($column.list && $column.htmlType == "datetime")
                      <el-table-column label="$comment" prop="${javaField}" width="240">
                        <template #default="scope">
                          <el-date-picker clearable
                                          style="width: 100%"
                                          v-model="scope.row.$javaField"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择$comment">
                          </el-date-picker>
                        </template>
                      </el-table-column>
                    #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") && "" != $column.dictType)
                      <el-table-column label="$comment" prop="${javaField}" width="150">
                        <template #default="scope">
                          <el-select v-model="scope.row.$javaField" placeholder="请选择$comment">
                            <el-option
                                v-for="dict in $column.dictType"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                    #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") && "" == $column.dictType)
                      <el-table-column label="$comment" prop="${javaField}" width="150">
                        <template #default="scope">
                          <el-select v-model="scope.row.$javaField" placeholder="请选择$comment">
                            <el-option label="请选择字典生成" value="" />
                          </el-select>
                        </template>
                      </el-table-column>
                    #end
                #end
            </el-table>
          #end
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="mini" @click="cancel">取 消</el-button>
          <el-button type="primary" size="mini" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- ${functionName}详情对话框 -->
    <el-dialog :title="title" v-model="openDetail" width="800px" :append-to="$refs['app-container']" draggable>
      <template #header="{ close, titleId, titleClass }">
        <span role="heading" aria-level="2" class="el-dialog__title">
          {{ title }}
        </span>
      </template>
      <el-form ref="${businessName}Ref" :model="form"  label-width="80px">
          #set($counter = 0)
          #foreach($column in $columns)
              #set($field=$column.javaField)
              #if($column.edit && !$column.pk)
                  #if(($column.usableColumn) || (!$column.superColumn))
                      #set($parentheseIndex=$column.columnComment.indexOf("（"))
                      #if($parentheseIndex != -1)
                          #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                      #else
                          #set($comment=$column.columnComment)
                      #end
                      #set($dictType=$column.dictType)
                      #if($counter % 2 == 0)
          <el-row :gutter="20">
#end
#if($column.htmlType == "input")
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <div>
                  {{ form.${field} }}
                </div>
              </el-form-item>
            </el-col>
                      #elseif($column.htmlType == "select" && "" == $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <div>
                  {{ form.${field} }}
                </div>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "select" && $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <dict-tag :options="${dictType}" :value="form.${field}"/>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "checkbox" && "" == $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <div>
                  {{ form.${field} }}
                </div>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "checkbox" && $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <dict-tag :options="${dictType}" :value="form.${field}"/>

              </el-form-item>
            </el-col>
#elseif($column.htmlType == "radio" && "" == $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <div>
                  {{ form.${field} }}
                </div>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "radio" && $dictType)
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <dict-tag :options="${dictType}" :value="form.${field}"/>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "datetime")
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <el-date-picker clearable
                                style="width: 100%"
                                v-model="form.${field}"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="请选择${comment}">
                </el-date-picker>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "imageUpload")
            <el-col :span="12">
              <el-form-item label="${comment}" prop="${field}">
                <image-preview :src="form.${field}" :width="50" :height="50"/>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "fileUpload")
            <el-col :span="24">
              <el-form-item label="${comment}" prop="${field}">
                <file-upload v-model="form.${field}" disabled="true"/>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "editor")
            <el-col :span="12">
              <el-form-item label="${comment}">
                <div v-html="form.${field}" ></div>
              </el-form-item>
            </el-col>
#elseif($column.htmlType == "textarea")
            <el-col :span="24">
              <el-form-item label="${comment}" prop="${field}">
                <div>
                  {{ form.${field} }}
                </div>
              </el-form-item>
            </el-col>
#end
#set($counter = $counter + 1)
#if($counter % 2 == 0)
          </el-row>
          #end
      #end
  #end
#end
          #if($counter % 2 != 0)
            </el-row>
          #end
          #if($table.sub)
            <el-divider content-position="center">${subTable.functionName}信息</el-divider>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" icon="Plus" @click="handleAdd${subClassName}">添加</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" icon="Delete" @click="handleDelete${subClassName}">删除</el-button>
              </el-col>
            </el-row>
            <el-table :data="${subclassName}List" :row-class-name="row${subClassName}Index" @selection-change="handle${subClassName}SelectionChange" ref="${subclassName}">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="序号" align="center" prop="index" width="50"/>
                #foreach($column in $subTable.columns)
                    #set($javaField=$column.javaField)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                    #if($column.pk || $javaField == ${subTableFkclassName})
                    #elseif($column.list && $column.htmlType == "input")
                      <el-table-column label="$comment" prop="${javaField}" width="150">
                        <template #default="scope">
                          <el-input v-model="scope.row.$javaField" placeholder="请输入$comment" />
                        </template>
                      </el-table-column>
                    #elseif($column.list && $column.htmlType == "datetime")
                      <el-table-column label="$comment" prop="${javaField}" width="240">
                        <template #default="scope">
                          <el-date-picker clearable
                                          style="width: 100%"
                                          v-model="scope.row.$javaField"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择$comment">
                          </el-date-picker>
                        </template>
                      </el-table-column>
                    #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") && "" != $column.dictType)
                      <el-table-column label="$comment" prop="${javaField}" width="150">
                        <template #default="scope">
                          <el-select v-model="scope.row.$javaField" placeholder="请选择$comment">
                            <el-option
                                v-for="dict in $column.dictType"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                    #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") && "" == $column.dictType)
                      <el-table-column label="$comment" prop="${javaField}" width="150">
                        <template #default="scope">
                          <el-select v-model="scope.row.$javaField" placeholder="请选择$comment">
                            <el-option label="请选择字典生成" value="" />
                          </el-select>
                        </template>
                      </el-table-column>
                    #end
                #end
            </el-table>
          #end
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="mini" @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="800px"  :append-to="$refs['app-container']" draggable destroy-on-close>
      <el-upload
          ref="uploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的${functionName}数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="upload.open = false">取 消</el-button>
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="${BusinessName}">
import {get${BusinessName}, list${BusinessName}} from "@/api/";
import {getToken} from "@/utils/auth.js";

const { proxy } = getCurrentInstance();
      #if(${dicts} != '')
          #set($dictsNoSymbol=$dicts.replace("'", ""))
      const { ${dictsNoSymbol} } = proxy.useDict(${dicts});
      #end

  const ${businessName}List = ref([]);
      #if($table.sub)
      const ${subclassName}List = ref([]);
      #end

  // 列显隐信息
  const columns = ref([
      #foreach($column in $columns)
          #if($column.list)
            { key: $foreach.index, label: "$column.columnComment", visible: true }#if($foreach.hasNext),#end
          #end
      #end
  ]);

  const getColumnVisibility = (key) => {
    const column = columns.value.find(col => col.key === key);
    // 如果没有找到对应列配置，默认显示
    if (!column) return true;
    // 如果找到对应列配置，根据visible属性来控制显示
    return column.visible;
  };

  const open = ref(false);
  const openDetail = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
      #if($table.sub)
      const checked${subClassName} = ref([]);
      #end
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
      #foreach ($column in $columns)
          #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
              #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
          const daterange${AttrName} = ref([]);
          #end
      #end
  const defaultSort = ref({ prop: "createTime", order: "desc" });
  const router = useRouter();

  /*** 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/${controllerPrefix}/${businessName}/importData"
  });

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
        #foreach ($column in $columns)
            #if($column.query)
        $column.javaField: null#if($foreach.count != $columns.size()),#end
            #end
        #end
    },
    rules: {
        #foreach ($column in $columns)
            #if($column.required)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
        $column.javaField: [{ required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select" || $column.htmlType == "radio")"change"#else"blur"#end }]#if($foreach.count != $columns.size()),#end
            #end
        #end
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询${functionName}列表 */
  function getList() {
    loading.value = true;
      #foreach ($column in $columns)
          #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
            queryParams.value.params = {};
              #break
          #end
      #end
      #foreach ($column in $columns)
          #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
              #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            if (null != daterange${AttrName} && '' != daterange${AttrName}) {
              queryParams.value.params["begin${AttrName}"] = daterange${AttrName}.value[0];
              queryParams.value.params["end${AttrName}"] = daterange${AttrName}.value[1];
            }
          #end
      #end
    list${BusinessName}(queryParams.value).then(response => {
            ${businessName}List.value = response.data.rows;
      total.value = response.data.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    openDetail.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
        #foreach ($column in $columns)
            #if($column.htmlType == "checkbox")
        $column.javaField: []#if($foreach.count != $columns.size()),#end
            #else
        $column.javaField: null#if($foreach.count != $columns.size()),#end
            #end
        #end
    };
      #if($table.sub)
              ${subclassName}List.value = [];
      #end
    proxy.resetForm("${businessName}Ref");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
      #foreach ($column in $columns)
          #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
              #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            daterange${AttrName}.value = [];
          #end
      #end
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.${pkColumn.javaField});
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }


  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order;
    getList();
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加${functionName}";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _${pkColumn.javaField} = row.${pkColumn.javaField} || ids.value
    get${BusinessName}(_${pkColumn.javaField}).then(response => {
      form.value = response.data;
        #foreach ($column in $columns)
            #if($column.htmlType == "checkbox")
              form.value.$column.javaField = form.value.${column.javaField} ? form.value.${column.javaField}.split(",") : [];
            #end
        #end
        #if($table.sub)
                ${subclassName}List.value = response.data.${subclassName}List;
        #end
      open.value = true;
      title.value = "修改${functionName}";
    });
  }


  /** 详情按钮操作 */
  function handleDetail(row) {
    reset();
    const _${pkColumn.javaField} = row.${pkColumn.javaField} || ids.value
    get${BusinessName}(_${pkColumn.javaField}).then(response => {
      form.value = response.data;
        #foreach ($column in $columns)
            #if($column.htmlType == "checkbox")
              form.value.$column.javaField = form.value.${column.javaField} ? form.value.${column.javaField}.split(",") : [];
            #end
        #end
        #if($table.sub)
                ${subclassName}List.value = response.data.${subclassName}List;
        #end
      openDetail.value = true;
      title.value = "${functionName}详情";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.#[[$]]#refs["${businessName}Ref"].validate(valid => {
      if (valid) {
        #foreach ($column in $columns)
        #if($column.htmlType == "checkbox")
        form.value.$column.javaField = form.value.${column.javaField}.join(",");
        #end
        #end
        #if($table.sub)
        form.value.${subclassName}List = ${subclassName}List.value;
        #end
        if (form.value.${pkColumn.javaField} != null) {
          update${BusinessName}(form.value).then(response => {
            proxy.#[[$modal]]#.msgSuccess("修改成功");
            open.value = false;
            getList();
          }).catch(error => {
            #foreach ($column in $columns)
            #if($column.htmlType == "checkbox")
            form.value.$column.javaField = form.value.${column.javaField}.split(',').map(String);
            #end
            #end
          });
        } else {
          add${BusinessName}(form.value).then(response => {
            proxy.#[[$modal]]#.msgSuccess("新增成功");
            open.value = false;
            getList();
          }).catch(error => {
            #foreach ($column in $columns)
            #if($column.htmlType == "checkbox")
            form.value.$column.javaField = form.value.${column.javaField}.split(',').map(String);
            #end
            #end
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _${pkColumn.javaField}s = row.${pkColumn.javaField} || ids.value;
    proxy.#[[$modal]]#.confirm('是否确认删除${functionName}编号为"' + _${pkColumn.javaField}s + '"的数据项？').then(function() {
      return del${BusinessName}(_${pkColumn.javaField}s);
    }).then(() => {
      getList();
      proxy.#[[$modal]]#.msgSuccess("删除成功");
    }).catch(() => {});
  }

  #if($table.sub)
  /** ${subTable.functionName}序号 */
  function row${subClassName}Index({ row, rowIndex }) {
    row.index = rowIndex + 1;
  }

  /** ${subTable.functionName}添加按钮操作 */
  function handleAdd${subClassName}() {
    let obj = {};
    #foreach($column in $subTable.columns)
    #if($column.pk || $column.javaField == ${subTableFkclassName})
    #elseif($column.list && "" != $javaField)
    obj.$column.javaField = "";
    #end
    #end
    ${subclassName}List.value.push(obj);
  }

  /** ${subTable.functionName}删除按钮操作 */
  function handleDelete${subClassName}() {
    if (checked${subClassName}.value.length == 0) {
      proxy.#[[$modal]]#.msgError("请先选择要删除的${subTable.functionName}数据");
    } else {
      const ${subclassName}s = ${subclassName}List.value;
      const checked${subClassName}s = checked${subClassName}.value;
      ${subclassName}List.value = ${subclassName}s.filter(function(item) {
        return checked${subClassName}s.indexOf(item.index) == -1
      });
    }
  }

  /** 复选框选中数据 */
  function handle${subClassName}SelectionChange(selection) {
    checked${subClassName}.value = selection.map(item => item.index)
  }

  #end
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('${controllerPrefix}/${businessName}/export', {
      ...queryParams.value
    }, `${businessName}_#[[${new Date().getTime()}]]#.xlsx`)
  }

  /** ---------------- 导入相关操作 -----------------**/
  /** 导入按钮操作 */
  function handleImport() {
    upload.title = "${functionName}导入";
    upload.open = true;
  }

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download("system/user/importTemplate", {
    }, `${businessName}_template_#[[${new Date().getTime()}]]#.xlsx`)
  }

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs["uploadRef"].submit();
  };

  /**文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true;
  };

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs["uploadRef"].handleRemove(file);
    proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
    getList();
  };
  /** ---------------------------------**/

  function routeTo(link, row) {
    if (link !== "" && link.indexOf("http") !== -1) {
      window.location.href = link;
      return
    }
    if (link !== "") {
      if(link === router.currentRoute.value.path) {
        window.location.reload();
      } else {
        router.push({
          path: link,
          query: {
            id:row.id
          }
        });
      }
    }
  }

  getList();
</script>
