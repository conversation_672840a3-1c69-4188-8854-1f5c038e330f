package tech.qiantong.qdata.module.att.controller.admin.project.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import tech.qiantong.qdata.common.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目与用户关联关系 Response VO 对象 ATT_PROJECT_USER_REL
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Schema(description = "项目与用户关联关系 Response VO")
@Data
public class AttProjectUserRelRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "ID")
    @Schema(description = "ID")
    private Long id;

    @Excel(name = "项目空间ID")
    @Schema(description = "项目空间ID", example = "")
    private Long projectId;

    @Excel(name = "用户ID")
    @Schema(description = "用户ID", example = "")
    private Long userId;

    @Excel(name = "是否有效")
    @Schema(description = "是否有效", example = "")
    private Boolean validFlag;

    @Excel(name = "删除标志")
    @Schema(description = "删除标志", example = "")
    private Boolean delFlag;

    @Excel(name = "创建人")
    @Schema(description = "创建人", example = "")
    private String createBy;

    @Excel(name = "创建人id")
    @Schema(description = "创建人id", example = "")
    private Long creatorId;

    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "")
    private Date createTime;

    @Excel(name = "更新人")
    @Schema(description = "更新人", example = "")
    private String updateBy;

    @Excel(name = "更新人id")
    @Schema(description = "更新人id", example = "")
    private Long updaterId;

    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "")
    private Date updateTime;

    @Excel(name = "备注")
    @Schema(description = "备注", example = "")
    private String remark;

    @Excel(name = "用户名称")
    @Schema(description = "用户名称", example = "")
    private String userName;

    @Excel(name = "手机号码")
    @Schema(description = "手机号码", example = "")
    private String phoneNumber;

    @Excel(name = "部门名称")
    @Schema(description = "部门名称", example = "")
    private String deptName;

    @Excel(name = "用户昵称")
    @Schema(description = "用户昵称", example = "")
    private String nickName;

    @Schema(description = "用户id集合", example = "")
    private List<Long> userIdList;

    @Schema(description = "角色id集合", example = "")
    private List<Long> roleIdList;
}
