<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.att.dal.mapper.cat.AttAssetCatMapper">

    <resultMap type="AttAssetCatDO" id="AttAssetCatResult">
        <result property="id"    column="ID"    />
        <result property="name"    column="NAME"    />
        <result property="parentId"    column="PARENT_ID"    />
        <result property="sortOrder"    column="SORT_ORDER"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="code"    column="CODE"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectAttAssetCatVo">
        select ID, NAME, PARENT_ID, SORT_ORDER, DESCRIPTION, CODE, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from ATT_ASSET_CAT
    </sql>

    <select id="selectAttAssetCatList" parameterType="AttAssetCatDO" resultMap="AttAssetCatResult">
        <include refid="selectAttAssetCatVo"/>
        <where>
            <if test="name != null  and name != ''"> and NAME like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectAttAssetCatByID" parameterType="Long" resultMap="AttAssetCatResult">
        <include refid="selectAttAssetCatVo"/>
        where ID = #{id}
    </select>

    <insert id="insertAttAssetCat" parameterType="AttAssetCatDO">
        insert into ATT_ASSET_CAT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="name != null">NAME,</if>
            <if test="parentId != null">PARENT_ID,</if>
            <if test="sortOrder != null">SORT_ORDER,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="code != null">CODE,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="description != null">#{DESCRIPTION},</if>
            <if test="code != null">#{code},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAttAssetCat" parameterType="AttAssetCatDO">
        update ATT_ASSET_CAT
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">NAME = #{name},</if>
            <if test="parentId != null">PARENT_ID = #{parentId},</if>
            <if test="sortOrder != null">SORT_ORDER = #{sortOrder},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="code != null">CODE = #{code},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteAttAssetCatByID" parameterType="Long">
        delete from ATT_ASSET_CAT where ID = #{id}
    </delete>

    <delete id="deleteAttAssetCatByIDs" parameterType="String">
        delete from ATT_ASSET_CAT where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
