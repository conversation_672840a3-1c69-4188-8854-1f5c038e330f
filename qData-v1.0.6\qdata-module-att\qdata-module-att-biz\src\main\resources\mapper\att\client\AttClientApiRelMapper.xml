<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.att.dal.mapper.client.AttClientApiRelMapper">

    <resultMap type="AttClientApiRelDO" id="AttClientApiRelResult">
        <result property="id"    column="ID"    />
        <result property="clientId"    column="CLIENT_ID"    />
        <result property="apiId"    column="API_ID"    />
        <result property="pvFlag"    column="PV_FLAG"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="status"    column="STATUS"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="REMARK"    column="REMARK"    />
    </resultMap>

    <sql id="selectAttClientApiRelVo">
        select ID, CLIENT_ID, API_ID, PV_FLAG, START_TIME, END_TIME, STATUS, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from ATT_CLIENT_API_REL
    </sql>

    <select id="selectAttClientApiRelList" parameterType="AttClientApiRelDO" resultMap="AttClientApiRelResult">
        <include refid="selectAttClientApiRelVo"/>
        <where>
            <if test="clientId != null "> and CLIENT_ID = #{clientId}</if>
            <if test="apiId != null "> and API_ID = #{apiId}</if>
            <if test="pvFlag != null  and pvFlag != ''"> and PV_FLAG = #{pvFlag}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
        </where>
    </select>

    <select id="selectAttClientApiRelById" parameterType="Long" resultMap="AttClientApiRelResult">
        <include refid="selectAttClientApiRelVo"/>
        where ID = #{id}
    </select>

    <insert id="insertAttClientApiRel" parameterType="AttClientApiRelDO">
        insert into ATT_CLIENT_API_REL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="clientId != null">CLIENT_ID,</if>
            <if test="apiId != null">API_ID,</if>
            <if test="pvFlag != null">PV_FLAG,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="status != null">STATUS,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="REMARK != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="apiId != null">#{apiId},</if>
            <if test="pvFlag != null">#{pvFlag},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="REMARK != null">#{REMARK},</if>
         </trim>
    </insert>

    <update id="updateAttClientApiRel" parameterType="AttClientApiRelDO">
        update ATT_CLIENT_API_REL
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientId != null">CLIENT_ID = #{clientId},</if>
            <if test="apiId != null">API_ID = #{apiId},</if>
            <if test="pvFlag != null">PV_FLAG = #{pvFlag},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="REMARK != null">REMARK = #{REMARK},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteAttClientApiRelById" parameterType="Long">
        delete from ATT_CLIENT_API_REL where ID = #{id}
    </delete>

    <delete id="deleteAttClientApiRelByIds" parameterType="String">
        delete from ATT_CLIENT_API_REL where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
