<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.att.dal.mapper.client.AttClientMapper">

    <resultMap type="AttClientDO" id="AttClientResult">
        <result property="id"    column="ID"    />
        <result property="name"    column="NAME"    />
        <result property="type"    column="TYPE"    />
        <result property="secret"    column="SECRET"    />
        <result property="homepageUrl"    column="HOMEPAGE_URL"    />
        <result property="allowUrl"    column="ALLOW_URL"    />
        <result property="syncUrl"    column="SYNC_URL"    />
        <result property="logo"    column="LOGO"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="publicFlag"    column="PUBLIC_FLAG"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectAttClientVo">
        select ID, NAME, TYPE, SECRET, HOMEPAGE_URL, ALLOW_URL, SYNC_URL, LOGO, DESCRIPTION, PUBLIC_FLAG, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from ATT_CLIENT
    </sql>

    <select id="selectAttClientList" parameterType="AttClientDO" resultMap="AttClientResult">
        <include refid="selectAttClientVo"/>
        <where>
            <if test="name != null  and name != ''"> and NAME like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and TYPE = #{type}</if>
            <if test="secret != null  and secret != ''"> and SECRET = #{secret}</if>
            <if test="homepageUrl != null  and homepageUrl != ''"> and HOMEPAGE_URL = #{homepageUrl}</if>
            <if test="allowUrl != null  and allowUrl != ''"> and ALLOW_URL = #{allowUrl}</if>
            <if test="syncUrl != null  and syncUrl != ''"> and SYNC_URL = #{syncUrl}</if>
            <if test="logo != null  and logo != ''"> and LOGO = #{logo}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION = #{description}</if>
            <if test="publicFlag != null  and publicFlag != ''"> and PUBLIC_FLAG = #{publicFlag}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectAttClientById" parameterType="Long" resultMap="AttClientResult">
        <include refid="selectAttClientVo"/>
        where ID = #{id}
    </select>

    <insert id="insertAttClient" parameterType="AttClientDO">
        insert into ATT_CLIENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="name != null and name != ''">NAME,</if>
            <if test="type != null and type != ''">TYPE,</if>
            <if test="secret != null">SECRET,</if>
            <if test="homepageUrl != null">HOMEPAGE_URL,</if>
            <if test="allowUrl != null">ALLOW_URL,</if>
            <if test="syncUrl != null">SYNC_URL,</if>
            <if test="logo != null">LOGO,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="publicFlag != null">PUBLIC_FLAG,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="secret != null">#{secret},</if>
            <if test="homepageUrl != null">#{homepageUrl},</if>
            <if test="allowUrl != null">#{allowUrl},</if>
            <if test="syncUrl != null">#{syncUrl},</if>
            <if test="logo != null">#{logo},</if>
            <if test="description != null">#{description},</if>
            <if test="publicFlag != null">#{publicFlag},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAttClient" parameterType="AttClientDO">
        update ATT_CLIENT
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">NAME = #{name},</if>
            <if test="type != null and type != ''">TYPE = #{type},</if>
            <if test="secret != null">SECRET = #{secret},</if>
            <if test="homepageUrl != null">HOMEPAGE_URL = #{homepageUrl},</if>
            <if test="allowUrl != null">ALLOW_URL = #{allowUrl},</if>
            <if test="syncUrl != null">SYNC_URL = #{syncUrl},</if>
            <if test="logo != null">LOGO = #{logo},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="publicFlag != null">PUBLIC_FLAG = #{publicFlag},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteAttClientById" parameterType="Long">
        delete from ATT_CLIENT where ID = #{id}
    </delete>

    <delete id="deleteAttClientByIds" parameterType="String">
        delete from ATT_CLIENT where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
