<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.att.dal.mapper.project.AttProjectUserRelMapper">

    <resultMap type="AttProjectUserRelDO" id="AttProjectUserRelResult">
        <result property="id"    column="ID"    />
        <result property="projectId"    column="PROJECT_ID"    />
        <result property="userId"    column="USER_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectAttProjectUserRelVo">
        select ID, PROJECT_ID, USER_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from ATT_PROJECT_USER_REL
    </sql>

    <select id="selectAttProjectUserRelList" parameterType="AttProjectUserRelDO" resultMap="AttProjectUserRelResult">
        <include refid="selectAttProjectUserRelVo"/>
        <where>
            <if test="projectId != null "> and PROJECT_ID = #{projectId}</if>
            <if test="userId != null "> and USER_ID = #{userId}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectAttProjectUserRelById" parameterType="Long" resultMap="AttProjectUserRelResult">
        <include refid="selectAttProjectUserRelVo"/>
        where ID = #{id}
    </select>

    <insert id="insertAttProjectUserRel" parameterType="AttProjectUserRelDO">
        insert into ATT_PROJECT_USER_REL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="projectId != null">PROJECT_ID,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateAttProjectUserRel" parameterType="AttProjectUserRelDO">
        update ATT_PROJECT_USER_REL
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">PROJECT_ID = #{projectId},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteAttProjectUserRelById" parameterType="Long">
        delete from ATT_PROJECT_USER_REL where ID = #{id}
    </delete>

    <delete id="deleteAttProjectUserRelByIds" parameterType="String">
        delete from ATT_PROJECT_USER_REL where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
