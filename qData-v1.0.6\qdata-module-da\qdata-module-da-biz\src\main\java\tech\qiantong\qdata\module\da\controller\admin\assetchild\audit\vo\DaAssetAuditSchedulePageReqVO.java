package tech.qiantong.qdata.module.da.controller.admin.assetchild.audit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import tech.qiantong.qdata.common.core.page.PageParam;

/**
 * 资产稽查调度 Request VO 对象 DA_ASSET_AUDIT_SCHEDULE
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Schema(description = "资产稽查调度 Request VO")
@Data
public class DaAssetAuditSchedulePageReqVO extends PageParam {

    private static final long serialVersionUID = 1L;
        @Schema(description = "ID", example = "")
        private Long id;
    @Schema(description = "资产ID", example = "")
    private Long assetId;

    @Schema(description = "是否开启稽查调度", example = "")
    private String scheduleFlag;

    @Schema(description = "cron执行表达式", example = "")
    private String cronExpression;

    @Schema(description = "节点id", example = "")
    private Long nodeId;

    @Schema(description = "节点编码", example = "")
    private String nodeCode;

    @Schema(description = "任务id", example = "")
    private Long taskId;

    @Schema(description = "任务编码", example = "")
    private String taskCode;

    @Schema(description = "定时任务调度表id", example = "")
    private Long systemJobId;




}
