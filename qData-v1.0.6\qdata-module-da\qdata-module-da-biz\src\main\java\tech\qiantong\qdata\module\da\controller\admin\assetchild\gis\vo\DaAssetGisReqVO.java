package tech.qiantong.qdata.module.da.controller.admin.assetchild.gis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 数据资产-地理空间服务 Request VO 对象 DA_ASSET_GIS
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Schema(description = "数据资产-地理空间服务 Request VO")
@Data
public class DaAssetGisReqVO {

    private static final long serialVersionUID = 1L;
        @Schema(description = "ID", example = "")
        private Long id;
    @Schema(description = "资产id", example = "")
    private Long assetId;

    @Schema(description = "服务地址", example = "")
    private String url;

    @Schema(description = "服务类型", example = "")
    private String type;

    @Schema(description = "请求方式", example = "")
    private String httpMethod;

    @Schema(description = "坐标系", example = "")
    private String coordinateSystem;

    Map<String, Object> queryParams;




}
