package tech.qiantong.qdata.module.da.controller.admin.assetchild.projectRel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import tech.qiantong.qdata.common.core.page.PageParam;

/**
 * 数据资产与项目关联关系 Request VO 对象 DA_ASSET_PROJECT_REL
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Schema(description = "数据资产与项目关联关系 Request VO")
@Data
public class DaAssetProjectRelPageReqVO extends PageParam {

    private static final long serialVersionUID = 1L;
        @Schema(description = "ID", example = "")
        private Long id;
    @Schema(description = "资产id", example = "")
    private Long assetId;

    @Schema(description = "项目id", example = "")
    private Long projectId;

    @Schema(description = "项目编码", example = "")
    private String projectCode;




}
