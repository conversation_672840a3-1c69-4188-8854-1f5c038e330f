<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.assetchild.audit.DaAssetAuditAlertMapper">

    <resultMap type="DaAssetAuditAlertDO" id="DaAssetAuditAlertResult">
        <result property="id"    column="ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="batchNo"    column="BATCH_NO"    />
        <result property="auditTime"    column="AUDIT_TIME"    />
        <result property="alertTime"    column="ALERT_TIME"    />
        <result property="alertMessage"    column="ALERT_MESSAGE"    />
        <result property="alertChannels"    column="ALERT_CHANNELS"    />
        <result property="alertChannelResult"    column="ALERT_CHANNEL_RESULT"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaAssetAuditAlertVo">
        select ID, ASSET_ID, BATCH_NO, AUDIT_TIME, ALERT_TIME, ALERT_MESSAGE, ALERT_CHANNELS, ALERT_CHANNEL_RESULT, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_ASSET_AUDIT_ALERT
    </sql>

    <select id="selectDaAssetAuditAlertList" parameterType="DaAssetAuditAlertDO" resultMap="DaAssetAuditAlertResult">
        <include refid="selectDaAssetAuditAlertVo"/>
        <where>
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="batchNo != null  and batchNo != ''"> and BATCH_NO = #{batchNo}</if>
            <if test="auditTime != null "> and AUDIT_TIME = #{auditTime}</if>
            <if test="alertTime != null "> and ALERT_TIME = #{alertTime}</if>
            <if test="alertMessage != null  and alertMessage != ''"> and ALERT_MESSAGE = #{alertMessage}</if>
            <if test="alertChannels != null  and alertChannels != ''"> and ALERT_CHANNELS = #{alertChannels}</if>
            <if test="alertChannelResult != null  and alertChannelResult != ''"> and ALERT_CHANNEL_RESULT = #{alertChannelResult}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaAssetAuditAlertById" parameterType="Long" resultMap="DaAssetAuditAlertResult">
        <include refid="selectDaAssetAuditAlertVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaAssetAuditAlert" parameterType="DaAssetAuditAlertDO">
        insert into DA_ASSET_AUDIT_ALERT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="batchNo != null">BATCH_NO,</if>
            <if test="auditTime != null">AUDIT_TIME,</if>
            <if test="alertTime != null">ALERT_TIME,</if>
            <if test="alertMessage != null">ALERT_MESSAGE,</if>
            <if test="alertChannels != null">ALERT_CHANNELS,</if>
            <if test="alertChannelResult != null">ALERT_CHANNEL_RESULT,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="alertTime != null">#{alertTime},</if>
            <if test="alertMessage != null">#{alertMessage},</if>
            <if test="alertChannels != null">#{alertChannels},</if>
            <if test="alertChannelResult != null">#{alertChannelResult},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaAssetAuditAlert" parameterType="DaAssetAuditAlertDO">
        update DA_ASSET_AUDIT_ALERT
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="batchNo != null">BATCH_NO = #{batchNo},</if>
            <if test="auditTime != null">AUDIT_TIME = #{auditTime},</if>
            <if test="alertTime != null">ALERT_TIME = #{alertTime},</if>
            <if test="alertMessage != null">ALERT_MESSAGE = #{alertMessage},</if>
            <if test="alertChannels != null">ALERT_CHANNELS = #{alertChannels},</if>
            <if test="alertChannelResult != null">ALERT_CHANNEL_RESULT = #{alertChannelResult},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaAssetAuditAlertById" parameterType="Long">
        delete from DA_ASSET_AUDIT_ALERT where ID = #{id}
    </delete>

    <delete id="deleteDaAssetAuditAlertByIds" parameterType="String">
        delete from DA_ASSET_AUDIT_ALERT where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
