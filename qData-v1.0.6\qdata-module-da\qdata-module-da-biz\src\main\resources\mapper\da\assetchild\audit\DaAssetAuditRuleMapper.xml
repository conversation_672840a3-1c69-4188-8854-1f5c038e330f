<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.assetchild.audit.DaAssetAuditRuleMapper">

    <resultMap type="DaAssetAuditRuleDO" id="DaAssetAuditRuleResult">
        <result property="id"    column="ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="tableName"    column="TABLE_NAME"    />
        <result property="columnName"    column="COLUMN_NAME"    />
        <result property="columnComment"    column="COLUMN_COMMENT"    />
        <result property="ruleName"    column="RULE_NAME"    />
        <result property="qualityDim"    column="QUALITY_DIM"    />
        <result property="ruleType"    column="RULE_TYPE"    />
        <result property="ruleLevel"    column="RULE_LEVEL"    />
        <result property="ruleDescription"    column="RULE_DESCRIPTION"    />
        <result property="ruleConfig"    column="RULE_CONFIG"    />
        <result property="totalCount"    column="TOTAL_COUNT"    />
        <result property="issueCount"    column="ISSUE_COUNT"    />
        <result property="auditTime"    column="AUDIT_TIME"    />
        <result property="batchNo"    column="BATCH_NO"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaAssetAuditRuleVo">
        select ID, ASSET_ID, TABLE_NAME, COLUMN_NAME, COLUMN_COMMENT, RULE_NAME, QUALITY_DIM, RULE_TYPE, RULE_LEVEL, RULE_DESCRIPTION, RULE_CONFIG, TOTAL_COUNT, ISSUE_COUNT, AUDIT_TIME, BATCH_NO, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_ASSET_AUDIT_RULE
    </sql>

    <select id="selectDaAssetAuditRuleList" parameterType="DaAssetAuditRuleDO" resultMap="DaAssetAuditRuleResult">
        <include refid="selectDaAssetAuditRuleVo"/>
        <where>
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="tableName != null  and tableName != ''"> and TABLE_NAME like concat('%', #{tableName}, '%')</if>
            <if test="columnName != null  and columnName != ''"> and COLUMN_NAME like concat('%', #{columnName}, '%')</if>
            <if test="columnComment != null  and columnComment != ''"> and COLUMN_COMMENT = #{columnComment}</if>
            <if test="ruleName != null  and ruleName != ''"> and RULE_NAME like concat('%', #{ruleName}, '%')</if>
            <if test="qualityDim != null  and qualityDim != ''"> and QUALITY_DIM = #{qualityDim}</if>
            <if test="ruleType != null  and ruleType != ''"> and RULE_TYPE = #{ruleType}</if>
            <if test="ruleLevel != null  and ruleLevel != ''"> and RULE_LEVEL = #{ruleLevel}</if>
            <if test="ruleDescription != null  and ruleDescription != ''"> and RULE_DESCRIPTION = #{ruleDescription}</if>
            <if test="ruleConfig != null  and ruleConfig != ''"> and RULE_CONFIG = #{ruleConfig}</if>
            <if test="totalCount != null "> and TOTAL_COUNT = #{totalCount}</if>
            <if test="issueCount != null "> and ISSUE_COUNT = #{issueCount}</if>
            <if test="auditTime != null "> and AUDIT_TIME = #{auditTime}</if>
            <if test="batchNo != null  and batchNo != ''"> and BATCH_NO = #{batchNo}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaAssetAuditRuleById" parameterType="Long" resultMap="DaAssetAuditRuleResult">
        <include refid="selectDaAssetAuditRuleVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaAssetAuditRule" parameterType="DaAssetAuditRuleDO">
        insert into DA_ASSET_AUDIT_RULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="tableName != null">TABLE_NAME,</if>
            <if test="columnName != null">COLUMN_NAME,</if>
            <if test="columnComment != null">COLUMN_COMMENT,</if>
            <if test="ruleName != null">RULE_NAME,</if>
            <if test="qualityDim != null">QUALITY_DIM,</if>
            <if test="ruleType != null">RULE_TYPE,</if>
            <if test="ruleLevel != null">RULE_LEVEL,</if>
            <if test="ruleDescription != null">RULE_DESCRIPTION,</if>
            <if test="ruleConfig != null">RULE_CONFIG,</if>
            <if test="totalCount != null">TOTAL_COUNT,</if>
            <if test="issueCount != null">ISSUE_COUNT,</if>
            <if test="auditTime != null">AUDIT_TIME,</if>
            <if test="batchNo != null">BATCH_NO,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="columnName != null">#{columnName},</if>
            <if test="columnComment != null">#{columnComment},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="qualityDim != null">#{qualityDim},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="ruleLevel != null">#{ruleLevel},</if>
            <if test="ruleDescription != null">#{ruleDescription},</if>
            <if test="ruleConfig != null">#{ruleConfig},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="issueCount != null">#{issueCount},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaAssetAuditRule" parameterType="DaAssetAuditRuleDO">
        update DA_ASSET_AUDIT_RULE
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="tableName != null">TABLE_NAME = #{tableName},</if>
            <if test="columnName != null">COLUMN_NAME = #{columnName},</if>
            <if test="columnComment != null">COLUMN_COMMENT = #{columnComment},</if>
            <if test="ruleName != null">RULE_NAME = #{ruleName},</if>
            <if test="qualityDim != null">QUALITY_DIM = #{qualityDim},</if>
            <if test="ruleType != null">RULE_TYPE = #{ruleType},</if>
            <if test="ruleLevel != null">RULE_LEVEL = #{ruleLevel},</if>
            <if test="ruleDescription != null">RULE_DESCRIPTION = #{ruleDescription},</if>
            <if test="ruleConfig != null">RULE_CONFIG = #{ruleConfig},</if>
            <if test="totalCount != null">TOTAL_COUNT = #{totalCount},</if>
            <if test="issueCount != null">ISSUE_COUNT = #{issueCount},</if>
            <if test="auditTime != null">AUDIT_TIME = #{auditTime},</if>
            <if test="batchNo != null">BATCH_NO = #{batchNo},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaAssetAuditRuleById" parameterType="Long">
        delete from DA_ASSET_AUDIT_RULE where ID = #{id}
    </delete>

    <delete id="deleteDaAssetAuditRuleByIds" parameterType="String">
        delete from DA_ASSET_AUDIT_RULE where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
