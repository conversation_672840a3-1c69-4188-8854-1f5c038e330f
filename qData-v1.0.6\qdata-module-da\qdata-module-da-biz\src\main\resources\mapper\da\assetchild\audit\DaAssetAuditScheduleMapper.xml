<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.assetchild.audit.DaAssetAuditScheduleMapper">

    <resultMap type="DaAssetAuditScheduleDO" id="DaAssetAuditScheduleResult">
        <result property="id"    column="ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="scheduleFlag"    column="SCHEDULE_FLAG"    />
        <result property="cronExpression"    column="CRON_EXPRESSION"    />
        <result property="nodeId"    column="NODE_ID"    />
        <result property="nodeCode"    column="NODE_CODE"    />
        <result property="taskId"    column="TASK_ID"    />
        <result property="taskCode"    column="TASK_CODE"    />
        <result property="systemJobId"    column="SYSTEM_JOB_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaAssetAuditScheduleVo">
        select ID, ASSET_ID, SCHEDULE_FLAG, CRON_EXPRESSION, NODE_ID, NODE_CODE, TASK_ID, TASK_CODE, SYSTEM_JOB_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_ASSET_AUDIT_SCHEDULE
    </sql>

    <select id="selectDaAssetAuditScheduleList" parameterType="DaAssetAuditScheduleDO" resultMap="DaAssetAuditScheduleResult">
        <include refid="selectDaAssetAuditScheduleVo"/>
        <where>
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="scheduleFlag != null  and scheduleFlag != ''"> and SCHEDULE_FLAG = #{scheduleFlag}</if>
            <if test="cronExpression != null  and cronExpression != ''"> and CRON_EXPRESSION = #{cronExpression}</if>
            <if test="nodeId != null "> and NODE_ID = #{nodeId}</if>
            <if test="nodeCode != null  and nodeCode != ''"> and NODE_CODE = #{nodeCode}</if>
            <if test="taskId != null "> and TASK_ID = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and TASK_CODE = #{taskCode}</if>
            <if test="systemJobId != null "> and SYSTEM_JOB_ID = #{systemJobId}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaAssetAuditScheduleById" parameterType="Long" resultMap="DaAssetAuditScheduleResult">
        <include refid="selectDaAssetAuditScheduleVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaAssetAuditSchedule" parameterType="DaAssetAuditScheduleDO">
        insert into DA_ASSET_AUDIT_SCHEDULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="scheduleFlag != null">SCHEDULE_FLAG,</if>
            <if test="cronExpression != null">CRON_EXPRESSION,</if>
            <if test="nodeId != null">NODE_ID,</if>
            <if test="nodeCode != null">NODE_CODE,</if>
            <if test="taskId != null">TASK_ID,</if>
            <if test="taskCode != null">TASK_CODE,</if>
            <if test="systemJobId != null">SYSTEM_JOB_ID,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="scheduleFlag != null">#{scheduleFlag},</if>
            <if test="cronExpression != null">#{cronExpression},</if>
            <if test="nodeId != null">#{nodeId},</if>
            <if test="nodeCode != null">#{nodeCode},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="systemJobId != null">#{systemJobId},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaAssetAuditSchedule" parameterType="DaAssetAuditScheduleDO">
        update DA_ASSET_AUDIT_SCHEDULE
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="scheduleFlag != null">SCHEDULE_FLAG = #{scheduleFlag},</if>
            <if test="cronExpression != null">CRON_EXPRESSION = #{cronExpression},</if>
            <if test="nodeId != null">NODE_ID = #{nodeId},</if>
            <if test="nodeCode != null">NODE_CODE = #{nodeCode},</if>
            <if test="taskId != null">TASK_ID = #{taskId},</if>
            <if test="taskCode != null">TASK_CODE = #{taskCode},</if>
            <if test="systemJobId != null">SYSTEM_JOB_ID = #{systemJobId},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaAssetAuditScheduleById" parameterType="Long">
        delete from DA_ASSET_AUDIT_SCHEDULE where ID = #{id}
    </delete>

    <delete id="deleteDaAssetAuditScheduleByIds" parameterType="String">
        delete from DA_ASSET_AUDIT_SCHEDULE where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
