<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.assetchild.geo.DaAssetGeoMapper">

    <resultMap type="DaAssetGeoDO" id="DaAssetGeoResult">
        <result property="id"    column="ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="fileName"    column="FILE_NAME"    />
        <result property="fileUrl"    column="FILE_URL"    />
        <result property="fileType"    column="FILE_TYPE"    />
        <result property="elementType"    column="ELEMENT_TYPE"    />
        <result property="coordinateSystem"    column="COORDINATE_SYSTEM"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaAssetGeoVo">
        select ID, ASSET_ID, FILE_NAME, FILE_URL, ELEMENT_TYPE,FILE_TYPE, COORDINATE_SYSTEM, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_ASSET_GEO
    </sql>

    <select id="selectDaAssetGeoList" parameterType="DaAssetGeoDO" resultMap="DaAssetGeoResult">
        <include refid="selectDaAssetGeoVo"/>
        <where>
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="fileName != null  and fileName != ''"> and FILE_NAME like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and FILE_URL = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and FILE_TYPE = #{fileType}</if>
            <if test="elementType != null  and elementType != ''"> and ELEMENT_TYPE = #{elementType}</if>
            <if test="coordinateSystem != null  and coordinateSystem != ''"> and COORDINATE_SYSTEM = #{coordinateSystem}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaAssetGeoById" parameterType="Long" resultMap="DaAssetGeoResult">
        <include refid="selectDaAssetGeoVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaAssetGeo" parameterType="DaAssetGeoDO">
        insert into DA_ASSET_GEO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="fileName != null">FILE_NAME,</if>
            <if test="fileUrl != null">FILE_URL,</if>
            <if test="fileType != null">FILE_TYPE,</if>
            <if test="elementType != null">ELEMENT_TYPE,</if>
            <if test="coordinateSystem != null">COORDINATE_SYSTEM,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="elementType != null">#{elementType},</if>
            <if test="coordinateSystem != null">#{coordinateSystem},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaAssetGeo" parameterType="DaAssetGeoDO">
        update DA_ASSET_GEO
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="fileName != null">FILE_NAME = #{fileName},</if>
            <if test="fileUrl != null">FILE_URL = #{fileUrl},</if>
            <if test="fileType != null">FILE_TYPE = #{fileType},</if>
            <if test="elementType != null">ELEMENT_TYPE = #{elementType},</if>
            <if test="coordinateSystem != null">COORDINATE_SYSTEM = #{coordinateSystem},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaAssetGeoById" parameterType="Long">
        delete from DA_ASSET_GEO where ID = #{id}
    </delete>

    <delete id="deleteDaAssetGeoByIds" parameterType="String">
        delete from DA_ASSET_GEO where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
