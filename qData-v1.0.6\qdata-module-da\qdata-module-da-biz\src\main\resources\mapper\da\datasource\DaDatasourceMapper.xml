<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.datasource.DaDatasourceMapper">

    <resultMap type="DaDatasourceDO" id="DaDatasourceResult">
        <result property="id"    column="ID"    />
        <result property="datasourceName"    column="DATASOURCE_NAME"    />
        <result property="datasourceType"    column="DATASOURCE_TYPE"    />
        <result property="datasourceConfig"    column="DATASOURCE_CONFIG"    />
        <result property="ip"    column="IP"    />
        <result property="port"    column="PORT"    />
        <result property="listCount"    column="LIST_COUNT"    />
        <result property="syncCount"    column="SYNC_COUNT"    />
        <result property="dataSize"    column="DATA_SIZE"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaDatasourceVo">
        select ID, DATASOURCE_NAME, DATASOURCE_TYPE, DATASOURCE_CONFIG, IP, PORT, LIST_COUNT, SYNC_COUNT, DATA_SIZE, DESCRIPTION, VALID_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_DATASOURCE
    </sql>

    <select id="selectDaDatasourceList" parameterType="DaDatasourceDO" resultMap="DaDatasourceResult">
        <include refid="selectDaDatasourceVo"/>
        <where>
            <if test="datasourceName != null  and datasourceName != ''"> and DATASOURCE_NAME like concat('%', #{datasourceName}, '%')</if>
            <if test="datasourceType != null  and datasourceType != ''"> and DATASOURCE_TYPE = #{datasourceType}</if>
            <if test="datasourceConfig != null  and datasourceConfig != ''"> and DATASOURCE_CONFIG = #{datasourceConfig}</if>
            <if test="ip != null  and ip != ''"> and IP = #{ip}</if>
            <if test="port != null "> and PORT = #{port}</if>
            <if test="listCount != null "> and LIST_COUNT = #{listCount}</if>
            <if test="syncCount != null "> and SYNC_COUNT = #{syncCount}</if>
            <if test="dataSize != null "> and DATA_SIZE = #{dataSize}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION = #{description}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaDatasourceById" parameterType="Long" resultMap="DaDatasourceResult">
        <include refid="selectDaDatasourceVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaDatasource" parameterType="DaDatasourceDO">
        insert into DA_DATASOURCE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="datasourceName != null">DATASOURCE_NAME,</if>
            <if test="datasourceType != null">DATASOURCE_TYPE,</if>
            <if test="datasourceConfig != null">DATASOURCE_CONFIG,</if>
            <if test="ip != null">IP,</if>
            <if test="port != null">PORT,</if>
            <if test="listCount != null">LIST_COUNT,</if>
            <if test="syncCount != null">SYNC_COUNT,</if>
            <if test="dataSize != null">DATA_SIZE,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="datasourceName != null">#{datasourceName},</if>
            <if test="datasourceType != null">#{datasourceType},</if>
            <if test="datasourceConfig != null">#{datasourceConfig},</if>
            <if test="ip != null">#{ip},</if>
            <if test="port != null">#{port},</if>
            <if test="listCount != null">#{listCount},</if>
            <if test="syncCount != null">#{syncCount},</if>
            <if test="dataSize != null">#{dataSize},</if>
            <if test="description != null">#{description},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaDatasource" parameterType="DaDatasourceDO">
        update DA_DATASOURCE
        <trim prefix="SET" suffixOverrides=",">
            <if test="datasourceName != null">DATASOURCE_NAME = #{datasourceName},</if>
            <if test="datasourceType != null">DATASOURCE_TYPE = #{datasourceType},</if>
            <if test="datasourceConfig != null">DATASOURCE_CONFIG = #{datasourceConfig},</if>
            <if test="ip != null">IP = #{ip},</if>
            <if test="port != null">PORT = #{port},</if>
            <if test="listCount != null">LIST_COUNT = #{listCount},</if>
            <if test="syncCount != null">SYNC_COUNT = #{syncCount},</if>
            <if test="dataSize != null">DATA_SIZE = #{dataSize},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaDatasourceById" parameterType="Long">
        delete from DA_DATASOURCE where ID = #{id}
    </delete>

    <delete id="deleteDaDatasourceByIds" parameterType="String">
        delete from DA_DATASOURCE where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getDataSourceByAsset" resultType="DaDatasourceDO">
        SELECT
            t.*
        FROM
            DA_DATASOURCE t
                INNER JOIN (
                SELECT DISTINCT
                    DATASOURCE_ID
                FROM
                    DA_ASSET
                WHERE
                    DEL_FLAG = '0'
            ) unique_datasource_ids ON
                t.ID = unique_datasource_ids.DATASOURCE_ID
        WHERE
            t.del_flag = 0;
    </select>
</mapper>
