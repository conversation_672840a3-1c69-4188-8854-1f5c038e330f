<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.discovery.DaDiscoveryTableMapper">

    <resultMap type="DaDiscoveryTableDO" id="DaDiscoveryTableResult">
        <result property="id"    column="ID"    />
        <result property="taskId"    column="TASK_ID"    />
        <result property="tableName"    column="TABLE_NAME"    />
        <result property="tableComment"    column="TABLE_COMMENT"    />
        <result property="dataCount"    column="DATA_COUNT"    />
        <result property="fieldCount"    column="FIELD_COUNT"    />
        <result property="changeFlag"    column="CHANGE_FLAG"    />
        <result property="status"    column="STATUS"    />
        <result property="ignoreFlag"    column="IGNORE_FLAG"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaDiscoveryTableVo">
        select ID, TASK_ID, TABLE_NAME, TABLE_COMMENT, DATA_COUNT, FIELD_COUNT, CHANGE_FLAG, STATUS, IGNORE_FLAG, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_DISCOVERY_TABLE
    </sql>

    <select id="selectDaDiscoveryTableList" parameterType="DaDiscoveryTableDO" resultMap="DaDiscoveryTableResult">
        <include refid="selectDaDiscoveryTableVo"/>
        <where>
            <if test="taskId != null  and taskId != ''"> and TASK_ID = #{taskId}</if>
            <if test="tableName != null  and tableName != ''"> and TABLE_NAME like concat('%', #{tableName}, '%')</if>
            <if test="tableComment != null  and tableComment != ''"> and TABLE_COMMENT = #{tableComment}</if>
            <if test="dataCount != null "> and DATA_COUNT = #{dataCount}</if>
            <if test="fieldCount != null "> and FIELD_COUNT = #{fieldCount}</if>
            <if test="changeFlag != null  and changeFlag != ''"> and CHANGE_FLAG = #{changeFlag}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
            <if test="ignoreFlag != null  and ignoreFlag != ''"> and IGNORE_FLAG = #{ignoreFlag}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaDiscoveryTableById" parameterType="Long" resultMap="DaDiscoveryTableResult">
        <include refid="selectDaDiscoveryTableVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaDiscoveryTable" parameterType="DaDiscoveryTableDO">
        insert into DA_DISCOVERY_TABLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="taskId != null">TASK_ID,</if>
            <if test="tableName != null">TABLE_NAME,</if>
            <if test="tableComment != null">TABLE_COMMENT,</if>
            <if test="dataCount != null">DATA_COUNT,</if>
            <if test="fieldCount != null">FIELD_COUNT,</if>
            <if test="changeFlag != null">CHANGE_FLAG,</if>
            <if test="status != null">STATUS,</if>
            <if test="ignoreFlag != null">IGNORE_FLAG,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="tableComment != null">#{tableComment},</if>
            <if test="dataCount != null">#{dataCount},</if>
            <if test="fieldCount != null">#{fieldCount},</if>
            <if test="changeFlag != null">#{changeFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="ignoreFlag != null">#{ignoreFlag},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaDiscoveryTable" parameterType="DaDiscoveryTableDO">
        update DA_DISCOVERY_TABLE
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">TASK_ID = #{taskId},</if>
            <if test="tableName != null">TABLE_NAME = #{tableName},</if>
            <if test="tableComment != null">TABLE_COMMENT = #{tableComment},</if>
            <if test="dataCount != null">DATA_COUNT = #{dataCount},</if>
            <if test="fieldCount != null">FIELD_COUNT = #{fieldCount},</if>
            <if test="changeFlag != null">CHANGE_FLAG = #{changeFlag},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="ignoreFlag != null">IGNORE_FLAG = #{ignoreFlag},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaDiscoveryTableById" parameterType="Long">
        delete from DA_DISCOVERY_TABLE where ID = #{id}
    </delete>

    <delete id="deleteDaDiscoveryTableByIds" parameterType="String">
        delete from DA_DISCOVERY_TABLE where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
