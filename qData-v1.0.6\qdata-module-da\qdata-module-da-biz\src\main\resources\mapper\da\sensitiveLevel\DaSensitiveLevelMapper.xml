<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.da.dal.mapper.sensitiveLevel.DaSensitiveLevelMapper">

    <resultMap type="DaSensitiveLevelDO" id="DaSensitiveLevelResult">
        <result property="id"    column="ID"    />
        <result property="sensitiveLevel"    column="SENSITIVE_LEVEl"    />
        <result property="sensitiveRule"    column="SENSITIVE_RULE"    />
        <result property="startCharLoc"    column="START_CHAR_LOC"    />
        <result property="endCharLoc"    column="END_CHAR_LOC"    />
        <result property="maskCharacter"    column="MASK_CHARACTER"    />
        <result property="onlineFlag"    column="ONLINE_FLAG"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDaSensitiveLevelVo">
        select ID, SENSITIVE_LEVEl, SENSITIVE_RULE, START_CHAR_LOC, END_CHAR_LOC, MASK_CHARACTER, ONLINE_FLAG, DESCRIPTION, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DA_SENSITIVE_LEVEL
    </sql>

    <select id="selectDaSensitiveLevelList" parameterType="DaSensitiveLevelDO" resultMap="DaSensitiveLevelResult">
        <include refid="selectDaSensitiveLevelVo"/>
        <where>
            <if test="sensitiveLevel != null  and sensitiveLevel != ''"> and SENSITIVE_LEVEl = #{sensitiveLevel}</if>
            <if test="sensitiveRule != null  and sensitiveRule != ''"> and SENSITIVE_RULE = #{sensitiveRule}</if>
            <if test="startCharLoc != null "> and START_CHAR_LOC = #{startCharLoc}</if>
            <if test="endCharLoc != null "> and END_CHAR_LOC = #{endCharLoc}</if>
            <if test="maskCharacter != null  and maskCharacter != ''"> and MASK_CHARACTER = #{maskCharacter}</if>
            <if test="onlineFlag != null  and onlineFlag != ''"> and ONLINE_FLAG = #{onlineFlag}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION = #{description}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDaSensitiveLevelById" parameterType="Long" resultMap="DaSensitiveLevelResult">
        <include refid="selectDaSensitiveLevelVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDaSensitiveLevel" parameterType="DaSensitiveLevelDO">
        insert into DA_SENSITIVE_LEVEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="sensitiveLevel != null">SENSITIVE_LEVEl,</if>
            <if test="sensitiveRule != null">SENSITIVE_RULE,</if>
            <if test="startCharLoc != null">START_CHAR_LOC,</if>
            <if test="endCharLoc != null">END_CHAR_LOC,</if>
            <if test="maskCharacter != null">MASK_CHARACTER,</if>
            <if test="onlineFlag != null">ONLINE_FLAG,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sensitiveLevel != null">#{sensitiveLevel},</if>
            <if test="sensitiveRule != null">#{sensitiveRule},</if>
            <if test="startCharLoc != null">#{startCharLoc},</if>
            <if test="endCharLoc != null">#{endCharLoc},</if>
            <if test="maskCharacter != null">#{maskCharacter},</if>
            <if test="onlineFlag != null">#{onlineFlag},</if>
            <if test="description != null">#{description},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDaSensitiveLevel" parameterType="DaSensitiveLevelDO">
        update DA_SENSITIVE_LEVEL
        <trim prefix="SET" suffixOverrides=",">
            <if test="sensitiveLevel != null">SENSITIVE_LEVEl = #{sensitiveLevel},</if>
            <if test="sensitiveRule != null">SENSITIVE_RULE = #{sensitiveRule},</if>
            <if test="startCharLoc != null">START_CHAR_LOC = #{startCharLoc},</if>
            <if test="endCharLoc != null">END_CHAR_LOC = #{endCharLoc},</if>
            <if test="maskCharacter != null">MASK_CHARACTER = #{maskCharacter},</if>
            <if test="onlineFlag != null">ONLINE_FLAG = #{onlineFlag},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDaSensitiveLevelById" parameterType="Long">
        delete from DA_SENSITIVE_LEVEL where ID = #{id}
    </delete>

    <delete id="deleteDaSensitiveLevelByIds" parameterType="String">
        delete from DA_SENSITIVE_LEVEL where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
