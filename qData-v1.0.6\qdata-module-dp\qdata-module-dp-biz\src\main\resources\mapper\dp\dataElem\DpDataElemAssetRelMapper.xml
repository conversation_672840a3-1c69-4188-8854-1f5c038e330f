<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dp.dal.mapper.dataElem.DpDataElemAssetRelMapper">

    <resultMap type="DpDataElemAssetRelDO" id="DpDataElemAssetRelResult">
        <result property="id"    column="ID"    />
        <result property="dataElemType"    column="DATA_ELEM_TYPE"    />
        <result property="dataElemId"    column="DATA_ELEM_ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="tableName"    column="TABLE_NAME"    />
        <result property="columnId"    column="COLUMN_ID"    />
        <result property="columnName"    column="COLUMN_NAME"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDpDataElemAssetRelVo">
        select ID, DATA_ELEM_TYPE, DATA_ELEM_ID, ASSET_ID, TABLE_NAME, COLUMN_ID, COLUMN_NAME, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DP_DATA_ELEM_ASSET_REL
    </sql>

    <select id="selectDpDataElemAssetRelList" parameterType="DpDataElemAssetRelDO" resultMap="DpDataElemAssetRelResult">
        <include refid="selectDpDataElemAssetRelVo"/>
        <where>
            <if test="dataElemType != null  and dataElemType != ''"> and DATA_ELEM_TYPE = #{dataElemType}</if>
            <if test="dataElemId != null  and dataElemId != ''"> and DATA_ELEM_ID = #{dataElemId}</if>
            <if test="assetId != null  and assetId != ''"> and ASSET_ID = #{assetId}</if>
            <if test="tableName != null  and tableName != ''"> and TABLE_NAME like concat('%', #{tableName}, '%')</if>
            <if test="columnId != null  and columnId != ''"> and COLUMN_ID = #{columnId}</if>
            <if test="columnName != null  and columnName != ''"> and COLUMN_NAME like concat('%', #{columnName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDpDataElemAssetRelById" parameterType="Long" resultMap="DpDataElemAssetRelResult">
        <include refid="selectDpDataElemAssetRelVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDpDataElemAssetRel" parameterType="DpDataElemAssetRelDO">
        insert into DP_DATA_ELEM_ASSET_REL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="dataElemType != null">DATA_ELEM_TYPE,</if>
            <if test="dataElemId != null">DATA_ELEM_ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="tableName != null">TABLE_NAME,</if>
            <if test="columnId != null">COLUMN_ID,</if>
            <if test="columnName != null">COLUMN_NAME,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dataElemType != null">#{dataElemType},</if>
            <if test="dataElemId != null">#{dataElemId},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="columnId != null">#{columnId},</if>
            <if test="columnName != null">#{columnName},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDpDataElemAssetRel" parameterType="DpDataElemAssetRelDO">
        update DP_DATA_ELEM_ASSET_REL
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataElemType != null">DATA_ELEM_TYPE = #{dataElemType},</if>
            <if test="dataElemId != null">DATA_ELEM_ID = #{dataElemId},</if>
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="tableName != null">TABLE_NAME = #{tableName},</if>
            <if test="columnId != null">COLUMN_ID = #{columnId},</if>
            <if test="columnName != null">COLUMN_NAME = #{columnName},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDpDataElemAssetRelById" parameterType="Long">
        delete from DP_DATA_ELEM_ASSET_REL where ID = #{id}
    </delete>

    <delete id="deleteDpDataElemAssetRelByIds" parameterType="String">
        delete from DP_DATA_ELEM_ASSET_REL where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
