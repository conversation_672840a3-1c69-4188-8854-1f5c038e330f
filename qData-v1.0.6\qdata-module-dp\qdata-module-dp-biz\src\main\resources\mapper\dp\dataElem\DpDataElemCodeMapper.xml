<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dp.dal.mapper.dataElem.DpDataElemCodeMapper">

    <resultMap type="DpDataElemCodeDO" id="DpDataElemCodeResult">
        <result property="id"    column="ID"    />
        <result property="dataElemId"    column="DATA_ELEM_ID"    />
        <result property="codeValue"    column="CODE_VALUE"    />
        <result property="codeName"    column="CODE_NAME"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDpDataElemCodeVo">
        select ID, DATA_ELEM_ID, CODE_VALUE, CODE_NAME, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DP_DATA_ELEM_CODE
    </sql>

    <select id="selectDpDataElemCodeList" parameterType="DpDataElemCodeDO" resultMap="DpDataElemCodeResult">
        <include refid="selectDpDataElemCodeVo"/>
        <where>
            <if test="dataElemId != null  and dataElemId != ''"> and DATA_ELEM_ID = #{dataElemId}</if>
            <if test="codeValue != null  and codeValue != ''"> and CODE_VALUE = #{codeValue}</if>
            <if test="codeName != null  and codeName != ''"> and CODE_NAME like concat('%', #{codeName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDpDataElemCodeById" parameterType="Long" resultMap="DpDataElemCodeResult">
        <include refid="selectDpDataElemCodeVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDpDataElemCode" parameterType="DpDataElemCodeDO">
        insert into DP_DATA_ELEM_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="dataElemId != null">DATA_ELEM_ID,</if>
            <if test="codeValue != null">CODE_VALUE,</if>
            <if test="codeName != null">CODE_NAME,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dataElemId != null">#{dataElemId},</if>
            <if test="codeValue != null">#{codeValue},</if>
            <if test="codeName != null">#{codeName},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDpDataElemCode" parameterType="DpDataElemCodeDO">
        update DP_DATA_ELEM_CODE
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataElemId != null">DATA_ELEM_ID = #{dataElemId},</if>
            <if test="codeValue != null">CODE_VALUE = #{codeValue},</if>
            <if test="codeName != null">CODE_NAME = #{codeName},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDpDataElemCodeById" parameterType="Long">
        delete from DP_DATA_ELEM_CODE where ID = #{id}
    </delete>

    <delete id="deleteDpDataElemCodeByIds" parameterType="String">
        delete from DP_DATA_ELEM_CODE where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
