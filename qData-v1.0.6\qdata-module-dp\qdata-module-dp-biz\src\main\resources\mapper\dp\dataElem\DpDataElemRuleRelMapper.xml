<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dp.dal.mapper.dataElem.DpDataElemRuleRelMapper">

    <resultMap type="DpDataElemRuleRelDO" id="DpDataElemRuleRelResult">
        <result property="id" column="ID"/>
        <result property="dataElemId" column="DATA_ELEM_ID"/>
        <result property="ruleType" column="RULE_TYPE"/>
        <result property="ruleId" column="RULE_ID"/>
        <result property="ruleConfig" column="RULE_CONFIG"/>
        <result property="validFlag" column="VALID_FLAG"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="createBy" column="CREATE_BY"/>
        <result property="creatorId" column="CREATOR_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateBy" column="UPDATE_BY"/>
        <result property="updaterId" column="UPDATER_ID"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="remark" column="REMARK"/>
    </resultMap>

    <sql id="selectDpDataElemRuleRelVo">
        select ID,
               DATA_ELEM_ID,
               RULE_TYPE,
               RULE_ID,
               RULE_CONFIG,
               VALID_FLAG,
               DEL_FLAG,
               CREATE_BY,
               CREATOR_ID,
               CREATE_TIME,
               UPDATE_BY,
               UPDATER_ID,
               UPDATE_TIME,
               REMARK
        from DP_DATA_ELEM_RULE_REL
    </sql>

    <select id="selectDpDataElemRuleRelList" parameterType="DpDataElemRuleRelDO" resultMap="DpDataElemRuleRelResult">
        <include refid="selectDpDataElemRuleRelVo"/>
        <where>
            <if test="dataElemId != null  and dataElemId != ''">and DATA_ELEM_ID = #{dataElemId}</if>
            <if test="ruleType != null  and ruleType != ''">and RULE_TYPE = #{ruleType}</if>
            <if test="ruleId != null  and ruleId != ''">and RULE_ID = #{ruleId}</if>
            <if test="ruleConfig != null  and ruleConfig != ''">and RULE_CONFIG = #{ruleConfig}</if>
        </where>
    </select>

    <select id="selectDpDataElemRuleRelById" parameterType="Long" resultMap="DpDataElemRuleRelResult">
        <include refid="selectDpDataElemRuleRelVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDpDataElemRuleRel" parameterType="DpDataElemRuleRelDO">
        insert into DP_DATA_ELEM_RULE_REL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="dataElemId != null">DATA_ELEM_ID,</if>
            <if test="ruleType != null">RULE_TYPE,</if>
            <if test="ruleId != null">RULE_ID,</if>
            <if test="ruleConfig != null">RULE_CONFIG,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dataElemId != null">#{dataElemId},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="ruleConfig != null">#{ruleConfig},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDpDataElemRuleRel" parameterType="DpDataElemRuleRelDO">
        update DP_DATA_ELEM_RULE_REL
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataElemId != null">DATA_ELEM_ID = #{dataElemId},</if>
            <if test="ruleType != null">RULE_TYPE = #{ruleType},</if>
            <if test="ruleId != null">RULE_ID = #{ruleId},</if>
            <if test="ruleConfig != null">RULE_CONFIG = #{ruleConfig},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDpDataElemRuleRelById" parameterType="Long">
        delete
        from DP_DATA_ELEM_RULE_REL
        where ID = #{id}
    </delete>

    <delete id="deleteDpDataElemRuleRelByIds" parameterType="String">
        delete from DP_DATA_ELEM_RULE_REL where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
