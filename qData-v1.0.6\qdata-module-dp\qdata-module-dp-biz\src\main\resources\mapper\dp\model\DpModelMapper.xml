<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dp.dal.mapper.model.DpModelMapper">

    <resultMap type="DpModelDO" id="DpModelResult">
        <result property="id"    column="ID"    />
        <result property="modelName"    column="MODEL_NAME"    />
        <result property="modelComment"    column="MODEL_COMMENT"    />
        <result property="catCode"    column="CAT_CODE"    />
        <result property="status"    column="STATUS"    />
        <result property="createType"    column="CREATE_TYPE"    />
        <result property="datasourceId"    column="DATASOURCE_ID"    />
        <result property="documentId"    column="DOCUMENT_ID"    />
        <result property="contact"    column="CONTACT"    />
        <result property="contactNumber"    column="CONTACT_NUMBER"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDpModelVo">
        select ID, MODEL_NAME, MODEL_COMMENT, CAT_CODE, STATUS, CREATE_TYPE, DATASOURCE_ID, DOCUMENT_ID, CONTACT, CONTACT_NUMBER, DESCRIPTION, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DP_MODEL
    </sql>

    <select id="selectDpModelList" parameterType="DpModelDO" resultMap="DpModelResult">
        <include refid="selectDpModelVo"/>
        <where>
            <if test="modelName != null  and modelName != ''"> and MODEL_NAME like concat('%', #{modelName}, '%')</if>
            <if test="modelComment != null  and modelComment != ''"> and MODEL_COMMENT = #{modelComment}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
            <if test="documentId != null  and documentId != ''"> and DOCUMENT_ID = #{documentId}</if>
        </where>
    </select>

    <select id="selectDpModelByID" parameterType="Long" resultMap="DpModelResult">
        <include refid="selectDpModelVo"/>
        where ID = #{ID}
    </select>

    <insert id="insertDpModel" parameterType="DpModelDO">
        insert into DP_MODEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ID != null">ID,</if>
            <if test="modelName != null and modelName != ''">MODEL_NAME,</if>
            <if test="modelComment != null and modelComment != ''">MODEL_COMMENT,</if>
            <if test="catCode != null and catCode != ''">CAT_CODE,</if>
            <if test="status != null and status != ''">STATUS,</if>
            <if test="createType != null and createType != ''">CREATE_TYPE,</if>
            <if test="datasourceId != null">DATASOURCE_ID,</if>
            <if test="documentId != null">DOCUMENT_ID,</if>
            <if test="contact != null">CONTACT,</if>
            <if test="contactNumber != null">CONTACT_NUMBER,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ID != null">#{ID},</if>
            <if test="modelName != null and modelName != ''">#{modelName},</if>
            <if test="modelComment != null and modelComment != ''">#{modelComment},</if>
            <if test="catCode != null and catCode != ''">#{catCode},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createType != null and createType != ''">#{createType},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="documentId != null">#{documentId},</if>
            <if test="contact != null">#{contact},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="description != null">#{description},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDpModel" parameterType="DpModelDO">
        update DP_MODEL
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelName != null and modelName != ''">MODEL_NAME = #{modelName},</if>
            <if test="modelComment != null and modelComment != ''">MODEL_COMMENT = #{modelComment},</if>
            <if test="catCode != null and catCode != ''">CAT_CODE = #{catCode},</if>
            <if test="status != null and status != ''">STATUS = #{status},</if>
            <if test="createType != null and createType != ''">CREATE_TYPE = #{createType},</if>
            <if test="datasourceId != null">DATASOURCE_ID = #{datasourceId},</if>
            <if test="documentId != null">DOCUMENT_ID = #{documentId},</if>
            <if test="contact != null">CONTACT = #{contact},</if>
            <if test="contactNumber != null">CONTACT_NUMBER = #{contactNumber},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{ID}
    </update>

    <delete id="deleteDpModelByID" parameterType="Long">
        delete from DP_MODEL where ID = #{ID}
    </delete>

    <delete id="deleteDpModelByIDs" parameterType="String">
        delete from DP_MODEL where ID in
        <foreach item="ID" collection="array" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </delete>
</mapper>
