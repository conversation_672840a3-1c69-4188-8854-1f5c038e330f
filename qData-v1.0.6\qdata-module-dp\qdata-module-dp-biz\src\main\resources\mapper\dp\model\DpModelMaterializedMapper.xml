<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dp.dal.mapper.model.DpModelMaterializedMapper">

    <resultMap type="DpModelMaterializedDO" id="DpModelMaterializedResult">
        <result property="id"    column="ID"    />
        <result property="modelName"    column="MODEL_NAME"    />
        <result property="modelAlias"    column="MODEL_ALIAS"    />
        <result property="modelId"    column="MODEL_ID"    />
        <result property="status"    column="STATUS"    />
        <result property="message"    column="MESSAGE"    />
        <result property="sqlCommand"    column="SQL_COMMAND"    />
        <result property="datasourceId"    column="DATASOURCE_ID"    />
        <result property="datasourceType"    column="DATASOURCE_TYPE"    />
        <result property="datasourceName"    column="DATASOURCE_NAME"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDpModelMaterializedVo">
        select ID, MODEL_NAME, MODEL_ALIAS, MODEL_ID, STATUS, MESSAGE, SQL_COMMAND, DATASOURCE_ID, DATASOURCE_TYPE, DATASOURCE_NAME, ASSET_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DP_MODEL_MATERIALIZED
    </sql>

    <select id="selectDpModelMaterializedList" parameterType="DpModelMaterializedDO" resultMap="DpModelMaterializedResult">
        <include refid="selectDpModelMaterializedVo"/>
        <where>
            <if test="modelName != null  and modelName != ''"> and MODEL_NAME like concat('%', #{modelName}, '%')</if>
            <if test="modelAlias != null  and modelAlias != ''"> and MODEL_ALIAS = #{modelAlias}</if>
            <if test="modelId != null "> and MODEL_ID = #{modelId}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
            <if test="message != null  and message != ''"> and MESSAGE = #{message}</if>
            <if test="sqlCommand != null  and sqlCommand != ''"> and SQL_COMMAND = #{sqlCommand}</if>
            <if test="datasourceId != null  and datasourceId != ''"> and DATASOURCE_ID = #{datasourceId}</if>
            <if test="datasourceType != null  and datasourceType != ''"> and DATASOURCE_TYPE = #{datasourceType}</if>
            <if test="datasourceName != null  and datasourceName != ''"> and DATASOURCE_NAME like concat('%', #{datasourceName}, '%')</if>
            <if test="assetId != null  and assetId != ''"> and ASSET_ID = #{assetId}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDpModelMaterializedById" parameterType="Long" resultMap="DpModelMaterializedResult">
        <include refid="selectDpModelMaterializedVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDpModelMaterialized" parameterType="DpModelMaterializedDO">
        insert into DP_MODEL_MATERIALIZED
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="modelName != null">MODEL_NAME,</if>
            <if test="modelAlias != null">MODEL_ALIAS,</if>
            <if test="modelId != null">MODEL_ID,</if>
            <if test="status != null">STATUS,</if>
            <if test="message != null">MESSAGE,</if>
            <if test="sqlCommand != null">SQL_COMMAND,</if>
            <if test="datasourceId != null">DATASOURCE_ID,</if>
            <if test="datasourceType != null">DATASOURCE_TYPE,</if>
            <if test="datasourceName != null">DATASOURCE_NAME,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="modelAlias != null">#{modelAlias},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="status != null">#{status},</if>
            <if test="message != null">#{message},</if>
            <if test="sqlCommand != null">#{sqlCommand},</if>
            <if test="datasourceId != null">#{datasourceId},</if>
            <if test="datasourceType != null">#{datasourceType},</if>
            <if test="datasourceName != null">#{datasourceName},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDpModelMaterialized" parameterType="DpModelMaterializedDO">
        update DP_MODEL_MATERIALIZED
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelName != null">MODEL_NAME = #{modelName},</if>
            <if test="modelAlias != null">MODEL_ALIAS = #{modelAlias},</if>
            <if test="modelId != null">MODEL_ID = #{modelId},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="message != null">MESSAGE = #{message},</if>
            <if test="sqlCommand != null">SQL_COMMAND = #{sqlCommand},</if>
            <if test="datasourceId != null">DATASOURCE_ID = #{datasourceId},</if>
            <if test="datasourceType != null">DATASOURCE_TYPE = #{datasourceType},</if>
            <if test="datasourceName != null">DATASOURCE_NAME = #{datasourceName},</if>
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDpModelMaterializedById" parameterType="Long">
        delete from DP_MODEL_MATERIALIZED where ID = #{id}
    </delete>

    <delete id="deleteDpModelMaterializedByIds" parameterType="String">
        delete from DP_MODEL_MATERIALIZED where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
