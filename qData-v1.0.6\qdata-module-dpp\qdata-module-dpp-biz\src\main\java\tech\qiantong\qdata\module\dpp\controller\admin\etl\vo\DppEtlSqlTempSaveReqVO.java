package tech.qiantong.qdata.module.dpp.controller.admin.etl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import tech.qiantong.qdata.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;

/**
 * 数据集成SQL模版 创建/修改 Request VO DPP_ETL_SQL_TEMP
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Schema(description = "数据集成SQL模版 Response VO")
@Data
public class DppEtlSqlTempSaveReqVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "名称", example = "")
    @Size(max = 256, message = "名称长度不能超过256个字符")
    private String name;

    @Schema(description = "类型", example = "")
    @Size(max = 256, message = "类型长度不能超过256个字符")
    private String type;

    @Schema(description = "内容", example = "")
    @Size(max = 256, message = "内容长度不能超过256个字符")
    private String content;

    @Schema(description = "描述", example = "")
    @Size(max = 256, message = "描述长度不能超过256个字符")
    private String description;


}
