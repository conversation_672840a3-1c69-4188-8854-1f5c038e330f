package tech.qiantong.qdata.module.dpp.dal.mapper.etl;

import tech.qiantong.qdata.common.core.page.PageResult;
import tech.qiantong.qdata.module.dpp.controller.admin.etl.vo.DppEtlSchedulerPageReqVO;
import tech.qiantong.qdata.module.dpp.dal.dataobject.etl.DppEtlSchedulerDO;
import tech.qiantong.qdata.mybatis.core.mapper.BaseMapperX;
import tech.qiantong.qdata.mybatis.core.query.LambdaQueryWrapperX;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 数据集成调度信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface DppEtlSchedulerMapper extends BaseMapperX<DppEtlSchedulerDO> {

    default PageResult<DppEtlSchedulerDO> selectPage(DppEtlSchedulerPageReqVO reqVO) {
        // 定义排序的字段（防止 SQL 注入，与数据库字段名称一致）
        Set<String> allowedColumns = new HashSet<>(Arrays.asList("id", "create_time", "update_time"));

        // 构造动态查询条件
        return selectPage(reqVO, new LambdaQueryWrapperX<DppEtlSchedulerDO>()
                .eqIfPresent(DppEtlSchedulerDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(DppEtlSchedulerDO::getTaskCode, reqVO.getTaskCode())
                .eqIfPresent(DppEtlSchedulerDO::getStartTime, reqVO.getStartTime())
                .eqIfPresent(DppEtlSchedulerDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(DppEtlSchedulerDO::getTimezoneId, reqVO.getTimezoneId())
                .eqIfPresent(DppEtlSchedulerDO::getCronExpression, reqVO.getCronExpression())
                .eqIfPresent(DppEtlSchedulerDO::getFailureStrategy, reqVO.getFailureStrategy())
                .eqIfPresent(DppEtlSchedulerDO::getDsId, reqVO.getDsId())
                .eqIfPresent(DppEtlSchedulerDO::getCreateTime, reqVO.getCreateTime())
                // 如果 reqVO.getName() 不为空，则添加 name 的精确匹配条件（name = '<name>'）
                // .likeIfPresent(DppEtlSchedulerDO::getName, reqVO.getName())
                // 按照 createTime 字段降序排序
                .orderBy(reqVO.getOrderByColumn(), reqVO.getIsAsc(), allowedColumns));
    }
}
