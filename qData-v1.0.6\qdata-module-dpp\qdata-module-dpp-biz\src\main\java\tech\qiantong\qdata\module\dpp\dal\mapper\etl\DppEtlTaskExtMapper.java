package tech.qiantong.qdata.module.dpp.dal.mapper.etl;

import tech.qiantong.qdata.common.core.page.PageResult;
import tech.qiantong.qdata.module.dpp.controller.admin.etl.vo.DppEtlTaskExtPageReqVO;
import tech.qiantong.qdata.module.dpp.dal.dataobject.etl.DppEtlTaskExtDO;
import tech.qiantong.qdata.mybatis.core.mapper.BaseMapperX;
import tech.qiantong.qdata.mybatis.core.query.LambdaQueryWrapperX;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 数据集成任务-扩展数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface DppEtlTaskExtMapper extends BaseMapperX<DppEtlTaskExtDO> {

    default PageResult<DppEtlTaskExtDO> selectPage(DppEtlTaskExtPageReqVO reqVO) {
        // 定义排序的字段（防止 SQL 注入，与数据库字段名称一致）
        Set<String> allowedColumns = new HashSet<>(Arrays.asList("id", "create_time", "update_time"));

        // 构造动态查询条件
        return selectPage(reqVO, new LambdaQueryWrapperX<DppEtlTaskExtDO>()
                .eqIfPresent(DppEtlTaskExtDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(DppEtlTaskExtDO::getEtlNodeId, reqVO.getEtlNodeId())
                .likeIfPresent(DppEtlTaskExtDO::getEtlNodeName, reqVO.getEtlNodeName())
                .eqIfPresent(DppEtlTaskExtDO::getEtlNodeCode, reqVO.getEtlNodeCode())
                .eqIfPresent(DppEtlTaskExtDO::getEtlNodeVersion, reqVO.getEtlNodeVersion())
                .eqIfPresent(DppEtlTaskExtDO::getEtlRelationId, reqVO.getEtlRelationId())
                // 如果 reqVO.getName() 不为空，则添加 name 的精确匹配条件（name = '<name>'）
                // .likeIfPresent(DppEtlTaskExtDO::getName, reqVO.getName())
                // 按照 createTime 字段降序排序
                .orderBy(reqVO.getOrderByColumn(), reqVO.getIsAsc(), allowedColumns));
    }
}
