<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dpp.dal.mapper.etl.DppEtlSchedulerMapper">

    <resultMap type="DppEtlSchedulerDO" id="DppEtlSchedulerResult">
        <result property="id"    column="ID"    />
        <result property="taskId"    column="TASK_ID"    />
        <result property="taskCode"    column="TASK_CODE"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="timezoneId"    column="TIMEZONE_ID"    />
        <result property="cronExpression"    column="CRON_EXPRESSION"    />
        <result property="failureStrategy"    column="FAILURE_STRATEGY"    />
        <result property="status"    column="STATUS"    />
        <result property="dsId"    column="DS_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDppEtlSchedulerVo">
        select ID, TASK_ID, TASK_CODE, START_TIME, STATUS, END_TIME, TIMEZONE_ID, CRON_EXPRESSION, FAILURE_STRATEGY, DS_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DPP_ETL_SCHEDULER
    </sql>

    <select id="selectDppEtlSchedulerList" parameterType="DppEtlSchedulerDO" resultMap="DppEtlSchedulerResult">
        <include refid="selectDppEtlSchedulerVo"/>
        <where>
            <if test="taskId != null "> and TASK_ID = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and TASK_CODE = #{taskCode}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
            <if test="timezoneId != null  and timezoneId != ''"> and TIMEZONE_ID = #{timezoneId}</if>
            <if test="cronExpression != null  and cronExpression != ''"> and CRON_EXPRESSION = #{cronExpression}</if>
            <if test="failureStrategy != null  and failureStrategy != ''"> and FAILURE_STRATEGY = #{failureStrategy}</if>
            <if test="dsId != null "> and DS_ID = #{dsId}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDppEtlSchedulerById" parameterType="Long" resultMap="DppEtlSchedulerResult">
        <include refid="selectDppEtlSchedulerVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDppEtlScheduler" parameterType="DppEtlSchedulerDO">
        insert into DPP_ETL_SCHEDULER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="taskId != null">TASK_ID,</if>
            <if test="taskCode != null">TASK_CODE,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="timezoneId != null">TIMEZONE_ID,</if>
            <if test="cronExpression != null">CRON_EXPRESSION,</if>
            <if test="failureStrategy != null">FAILURE_STRATEGY,</if>
            <if test="dsId != null">DS_ID,</if>
            <if test="status != null">STATUS,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="timezoneId != null">#{timezoneId},</if>
            <if test="cronExpression != null">#{cronExpression},</if>
            <if test="failureStrategy != null">#{failureStrategy},</if>
            <if test="dsId != null">#{dsId},</if>
            <if test="status != null">#{status},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDppEtlScheduler" parameterType="DppEtlSchedulerDO">
        update DPP_ETL_SCHEDULER
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">TASK_ID = #{taskId},</if>
            <if test="taskCode != null">TASK_CODE = #{taskCode},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="timezoneId != null">TIMEZONE_ID = #{timezoneId},</if>
            <if test="cronExpression != null">CRON_EXPRESSION = #{cronExpression},</if>
            <if test="failureStrategy != null">FAILURE_STRATEGY = #{failureStrategy},</if>
            <if test="dsId != null">DS_ID = #{dsId},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDppEtlSchedulerById" parameterType="Long">
        delete from DPP_ETL_SCHEDULER where ID = #{id}
    </delete>

    <delete id="deleteDppEtlSchedulerByIds" parameterType="String">
        delete from DPP_ETL_SCHEDULER where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
