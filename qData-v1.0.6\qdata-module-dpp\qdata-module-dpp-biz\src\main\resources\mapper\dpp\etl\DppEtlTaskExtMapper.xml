<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dpp.dal.mapper.etl.DppEtlTaskExtMapper">

    <resultMap type="DppEtlTaskExtDO" id="DppEtlTaskExtResult">
        <result property="id"    column="ID"    />
        <result property="taskId"    column="TASK_ID"    />
        <result property="etlNodeId"    column="ETL_NODE_ID"    />
        <result property="etlNodeName"    column="ETL_NODE_NAME"    />
        <result property="etlNodeCode"    column="ETL_NODE_CODE"    />
        <result property="etlNodeVersion"    column="ETL_NODE_VERSION"    />
        <result property="etlRelationId"    column="ETL_RELATION_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDppEtlTaskExtVo">
        select ID, TASK_ID, ETL_NODE_ID, ETL_NODE_NAME, ETL_NODE_CODE, ETL_NODE_VERSION, ETL_RELATION_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DPP_ETL_TASK_EXT
    </sql>

    <select id="selectDppEtlTaskExtList" parameterType="DppEtlTaskExtDO" resultMap="DppEtlTaskExtResult">
        <include refid="selectDppEtlTaskExtVo"/>
        <where>
            <if test="taskId != null "> and TASK_ID = #{taskId}</if>
            <if test="etlNodeId != null "> and ETL_NODE_ID = #{etlNodeId}</if>
            <if test="etlNodeName != null  and etlNodeName != ''"> and ETL_NODE_NAME like concat('%', #{etlNodeName}, '%')</if>
            <if test="etlNodeCode != null  and etlNodeCode != ''"> and ETL_NODE_CODE = #{etlNodeCode}</if>
            <if test="etlNodeVersion != null "> and ETL_NODE_VERSION = #{etlNodeVersion}</if>
            <if test="etlRelationId != null "> and ETL_RELATION_ID = #{etlRelationId}</if>
        </where>
    </select>

    <select id="selectDppEtlTaskExtById" parameterType="Long" resultMap="DppEtlTaskExtResult">
        <include refid="selectDppEtlTaskExtVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDppEtlTaskExt" parameterType="DppEtlTaskExtDO">
        insert into DPP_ETL_TASK_EXT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="taskId != null">TASK_ID,</if>
            <if test="etlNodeId != null">ETL_NODE_ID,</if>
            <if test="etlNodeName != null">ETL_NODE_NAME,</if>
            <if test="etlNodeCode != null">ETL_NODE_CODE,</if>
            <if test="etlNodeVersion != null">ETL_NODE_VERSION,</if>
            <if test="etlRelationId != null">ETL_RELATION_ID,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="etlNodeId != null">#{etlNodeId},</if>
            <if test="etlNodeName != null">#{etlNodeName},</if>
            <if test="etlNodeCode != null">#{etlNodeCode},</if>
            <if test="etlNodeVersion != null">#{etlNodeVersion},</if>
            <if test="etlRelationId != null">#{etlRelationId},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDppEtlTaskExt" parameterType="DppEtlTaskExtDO">
        update DPP_ETL_TASK_EXT
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">TASK_ID = #{taskId},</if>
            <if test="etlNodeId != null">ETL_NODE_ID = #{etlNodeId},</if>
            <if test="etlNodeName != null">ETL_NODE_NAME = #{etlNodeName},</if>
            <if test="etlNodeCode != null">ETL_NODE_CODE = #{etlNodeCode},</if>
            <if test="etlNodeVersion != null">ETL_NODE_VERSION = #{etlNodeVersion},</if>
            <if test="etlRelationId != null">ETL_RELATION_ID = #{etlRelationId},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDppEtlTaskExtById" parameterType="Long">
        delete from DPP_ETL_TASK_EXT where ID = #{id}
    </delete>

    <delete id="deleteDppEtlTaskExtByIds" parameterType="String">
        delete from DPP_ETL_TASK_EXT where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
