<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dpp.dal.mapper.etl.DppEtlTaskLogMapper">

    <resultMap type="DppEtlTaskLogDO" id="DppEtlTaskLogResult">
        <result property="id"    column="ID"    />
        <result property="type"    column="TYPE"    />
        <result property="name"    column="NAME"    />
        <result property="code"    column="CODE"    />
        <result property="version"    column="VERSION"    />
        <result property="projectId"    column="PROJECT_ID"    />
        <result property="projectCode"    column="PROJECT_CODE"    />
        <result property="personCharge"    column="PERSON_CHARGE"    />
        <result property="locations"    column="LOCATIONS"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="timeout"    column="TIMEOUT"    />
        <result property="extractionCount"    column="EXTRACTION_COUNT"    />
        <result property="writeCount"    column="WRITE_COUNT"    />
        <result property="status"    column="STATUS"    />
        <result property="dsId"    column="DS_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDppEtlTaskLogVo">
        select ID, TYPE, NAME, CODE, VERSION, PROJECT_ID, PROJECT_CODE, PERSON_CHARGE, LOCATIONS, DESCRIPTION, TIMEOUT, EXTRACTION_COUNT, WRITE_COUNT, STATUS, DS_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DPP_ETL_TASK_LOG
    </sql>

    <select id="selectDppEtlTaskLogList" parameterType="DppEtlTaskLogDO" resultMap="DppEtlTaskLogResult">
        <include refid="selectDppEtlTaskLogVo"/>
        <where>
            <if test="type != null  and type != ''"> and TYPE = #{type}</if>
            <if test="name != null  and name != ''"> and NAME like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and CODE = #{code}</if>
            <if test="version != null "> and VERSION = #{version}</if>
            <if test="projectId != null "> and PROJECT_ID = #{projectId}</if>
            <if test="projectCode != null  and projectCode != ''"> and PROJECT_CODE = #{projectCode}</if>
            <if test="personCharge != null  and personCharge != ''"> and PERSON_CHARGE = #{personCharge}</if>
            <if test="locations != null  and locations != ''"> and LOCATIONS = #{locations}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION = #{description}</if>
            <if test="timeout != null "> and TIMEOUT = #{timeout}</if>
            <if test="extractionCount != null "> and EXTRACTION_COUNT = #{extractionCount}</if>
            <if test="writeCount != null "> and WRITE_COUNT = #{writeCount}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
            <if test="dsId != null "> and DS_ID = #{dsId}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDppEtlTaskLogById" parameterType="Long" resultMap="DppEtlTaskLogResult">
        <include refid="selectDppEtlTaskLogVo"/>
        where ID = #{id}
    </select>
    <select id="queryMaxVersionByCode" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            MAX(VERSION)
        from
            DPP_ETL_TASK_LOG
        where
            CODE = #{taskCode}
            AND DEL_FLAG = '0'
    </select>

    <insert id="insertDppEtlTaskLog" parameterType="DppEtlTaskLogDO">
        insert into DPP_ETL_TASK_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="type != null">TYPE,</if>
            <if test="name != null">NAME,</if>
            <if test="code != null">CODE,</if>
            <if test="version != null">VERSION,</if>
            <if test="projectId != null">PROJECT_ID,</if>
            <if test="projectCode != null">PROJECT_CODE,</if>
            <if test="personCharge != null">PERSON_CHARGE,</if>
            <if test="locations != null">LOCATIONS,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="timeout != null">TIMEOUT,</if>
            <if test="extractionCount != null">EXTRACTION_COUNT,</if>
            <if test="writeCount != null">WRITE_COUNT,</if>
            <if test="status != null">STATUS,</if>
            <if test="dsId != null">DS_ID,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="version != null">#{version},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="projectCode != null">#{projectCode},</if>
            <if test="personCharge != null">#{personCharge},</if>
            <if test="locations != null">#{locations},</if>
            <if test="description != null">#{description},</if>
            <if test="timeout != null">#{timeout},</if>
            <if test="extractionCount != null">#{extractionCount},</if>
            <if test="writeCount != null">#{writeCount},</if>
            <if test="status != null">#{status},</if>
            <if test="dsId != null">#{dsId},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDppEtlTaskLog" parameterType="DppEtlTaskLogDO">
        update DPP_ETL_TASK_LOG
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">TYPE = #{type},</if>
            <if test="name != null">NAME = #{name},</if>
            <if test="code != null">CODE = #{code},</if>
            <if test="version != null">VERSION = #{version},</if>
            <if test="projectId != null">PROJECT_ID = #{projectId},</if>
            <if test="projectCode != null">PROJECT_CODE = #{projectCode},</if>
            <if test="personCharge != null">PERSON_CHARGE = #{personCharge},</if>
            <if test="locations != null">LOCATIONS = #{locations},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="timeout != null">TIMEOUT = #{timeout},</if>
            <if test="extractionCount != null">EXTRACTION_COUNT = #{extractionCount},</if>
            <if test="writeCount != null">WRITE_COUNT = #{writeCount},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="dsId != null">DS_ID = #{dsId},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDppEtlTaskLogById" parameterType="Long">
        delete from DPP_ETL_TASK_LOG where ID = #{id}
    </delete>

    <delete id="deleteDppEtlTaskLogByIds" parameterType="String">
        delete from DPP_ETL_TASK_LOG where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
