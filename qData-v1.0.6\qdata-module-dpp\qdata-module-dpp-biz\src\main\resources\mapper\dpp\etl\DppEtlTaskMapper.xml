<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.qiantong.qdata.module.dpp.dal.mapper.etl.DppEtlTaskMapper">

    <resultMap type="DppEtlTaskDO" id="DppEtlTaskResult">
        <result property="id"    column="ID"    />
        <result property="type"    column="TYPE"    />
        <result property="name"    column="NAME"    />
        <result property="code"    column="CODE"    />
        <result property="version"    column="VERSION"    />
        <result property="projectId"    column="PROJECT_ID"    />
        <result property="projectCode"    column="PROJECT_CODE"    />
        <result property="personCharge"    column="PERSON_CHARGE"    />
        <result property="locations"    column="LOCATIONS"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="timeout"    column="TIMEOUT"    />
        <result property="extractionCount"    column="EXTRACTION_COUNT"    />
        <result property="executionType"    column="EXECUTION_TYPE"    />
        <result property="writeCount"    column="WRITE_COUNT"    />
        <result property="status"    column="STATUS"    />
        <result property="dsId"    column="DS_ID"    />
        <result property="validFlag"    column="VALID_FLAG"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="creatorId"    column="CREATOR_ID"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updaterId"    column="UPDATER_ID"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="remark"    column="REMARK"    />
    </resultMap>

    <sql id="selectDppEtlTaskVo">
        select ID, TYPE, NAME, CODE, VERSION, PROJECT_ID,EXECUTION_TYPE, PROJECT_CODE, PERSON_CHARGE, LOCATIONS, DESCRIPTION, TIMEOUT, EXTRACTION_COUNT, WRITE_COUNT, STATUS, DS_ID, VALID_FLAG, DEL_FLAG, CREATE_BY, CREATOR_ID, CREATE_TIME, UPDATE_BY, UPDATER_ID, UPDATE_TIME, REMARK from DPP_ETL_TASK
    </sql>

    <select id="selectDppEtlTaskList" parameterType="DppEtlTaskDO" resultMap="DppEtlTaskResult">
        <include refid="selectDppEtlTaskVo"/>
        <where>
            <if test="type != null  and type != ''"> and TYPE = #{type}</if>
            <if test="name != null  and name != ''"> and NAME like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and CODE = #{code}</if>
            <if test="version != null "> and VERSION = #{version}</if>
            <if test="projectId != null "> and PROJECT_ID = #{projectId}</if>
            <if test="projectCode != null  and projectCode != ''"> and PROJECT_CODE = #{projectCode}</if>
            <if test="executionType != null  and executionType != ''"> and EXECUTION_TYPE = #{executionType}</if>
            <if test="personCharge != null  and personCharge != ''"> and PERSON_CHARGE = #{personCharge}</if>
            <if test="locations != null  and locations != ''"> and LOCATIONS = #{locations}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION = #{description}</if>
            <if test="timeout != null "> and TIMEOUT = #{timeout}</if>
            <if test="extractionCount != null "> and EXTRACTION_COUNT = #{extractionCount}</if>
            <if test="writeCount != null "> and WRITE_COUNT = #{writeCount}</if>
            <if test="status != null  and status != ''"> and STATUS = #{status}</if>
            <if test="dsId != null "> and DS_ID = #{dsId}</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
        </where>
    </select>

    <select id="selectDppEtlTaskById" parameterType="Long" resultMap="DppEtlTaskResult">
        <include refid="selectDppEtlTaskVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDppEtlTask" parameterType="DppEtlTaskDO">
        insert into DPP_ETL_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="type != null">TYPE,</if>
            <if test="name != null">NAME,</if>
            <if test="code != null">CODE,</if>
            <if test="version != null">VERSION,</if>
            <if test="projectId != null">PROJECT_ID,</if>
            <if test="projectCode != null">PROJECT_CODE,</if>
            <if test="personCharge != null">PERSON_CHARGE,</if>
            <if test="locations != null">LOCATIONS,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="timeout != null">TIMEOUT,</if>
            <if test="extractionCount != null">EXTRACTION_COUNT,</if>
            <if test="executionType != null">EXECUTION_TYPE,</if>
            <if test="writeCount != null">WRITE_COUNT,</if>
            <if test="status != null">STATUS,</if>
            <if test="dsId != null">DS_ID,</if>
            <if test="validFlag != null">VALID_FLAG,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="creatorId != null">CREATOR_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updaterId != null">UPDATER_ID,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="remark != null">REMARK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="version != null">#{version},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="projectCode != null">#{projectCode},</if>
            <if test="personCharge != null">#{personCharge},</if>
            <if test="locations != null">#{locations},</if>
            <if test="description != null">#{description},</if>
            <if test="timeout != null">#{timeout},</if>
            <if test="extractionCount != null">#{extractionCount},</if>
            <if test="executionType != null">#{executionType},</if>
            <if test="writeCount != null">#{writeCount},</if>
            <if test="status != null">#{status},</if>
            <if test="dsId != null">#{dsId},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDppEtlTask" parameterType="DppEtlTaskDO">
        update DPP_ETL_TASK
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">TYPE = #{type},</if>
            <if test="name != null">NAME = #{name},</if>
            <if test="code != null">CODE = #{code},</if>
            <if test="version != null">VERSION = #{version},</if>
            <if test="projectId != null">PROJECT_ID = #{projectId},</if>
            <if test="projectCode != null">PROJECT_CODE = #{projectCode},</if>
            <if test="personCharge != null">PERSON_CHARGE = #{personCharge},</if>
            <if test="locations != null">LOCATIONS = #{locations},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="timeout != null">TIMEOUT = #{timeout},</if>
            <if test="extractionCount != null">EXTRACTION_COUNT = #{extractionCount},</if>
            <if test="executionType != null">EXECUTION_TYPE = #{executionType},</if>
            <if test="writeCount != null">WRITE_COUNT = #{writeCount},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="dsId != null">DS_ID = #{dsId},</if>
            <if test="validFlag != null">VALID_FLAG = #{validFlag},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="creatorId != null">CREATOR_ID = #{creatorId},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updaterId != null">UPDATER_ID = #{updaterId},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="remark != null">REMARK = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDppEtlTaskById" parameterType="Long">
        delete from DPP_ETL_TASK where ID = #{id}
    </delete>

    <delete id="deleteDppEtlTaskByIds" parameterType="String">
        delete from DPP_ETL_TASK where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>



    <select id="checkTaskIdInSubTasks" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM DPP_ETL_TASK dt
        WHERE dt.DEL_FLAG = '0'
          AND dt.TYPE = '4'
          AND dt.id IN (
            SELECT rel.TASK_ID
            FROM DPP_ETL_TASK_NODE_REL rel
                     JOIN DPP_ETL_NODE node
                          ON (node.CODE = rel.PRE_NODE_CODE OR node.CODE = rel.POST_NODE_CODE)
            WHERE
                <choose>
                    <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                        CAST(
                            SUBSTRING_INDEX(
                                SUBSTRING_INDEX(
                                    node.PARAMETERS,
                                    '"subTaskId":',
                                    -1
                                ),
                                ',',
                                1
                            ) AS UNSIGNED
                        )
                    </when>
                    <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                        TO_NUMBER(
                        REGEXP_SUBSTR(node.PARAMETERS,
                        '"subTaskId"[[:space:]]*:[[:space:]]*([0-9]+)',
                        1, 1, NULL, 1)
                        )
                    </when>
                </choose> = #{id}
        );
    </select>

    <select id="checkTaskIdInDatasource" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM DPP_ETL_TASK dt
        WHERE dt.DEL_FLAG = '0'
          AND dt.TYPE != '4'
            <if test="projectIdList != null and projectIdList.size > 0">
                AND dt.PROJECT_ID in
                <foreach item="id" collection="projectIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
          AND dt.id IN (
            SELECT rel.TASK_ID
            FROM DPP_ETL_TASK_NODE_REL rel
                     JOIN DPP_ETL_NODE node
                          ON (node.CODE = rel.PRE_NODE_CODE OR node.CODE = rel.POST_NODE_CODE)
            WHERE
                <choose>
                    <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                        CAST(
                            SUBSTRING_INDEX(
                                SUBSTRING_INDEX(
                                    node.PARAMETERS,
                                    '"datasourceId":',
                                    -1
                                ),
                                ',',
                                1
                            ) AS UNSIGNED
                        )
                    </when>
                    <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                                TO_NUMBER(
                                REGEXP_SUBSTR(node.PARAMETERS,
                                '"datasourceId"[[:space:]]*:[[:space:]]*([0-9]+)',
                                1, 1, NULL, 1)
                                )
                    </when>
                </choose>
                        in
                        <foreach item="id" collection="datasourceIdList" open="(" separator="," close=")">
                            #{id}
                        </foreach>
        );
    </select>
    <select id="checkTaskIdInAsset" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM DPP_ETL_TASK dt
        WHERE dt.DEL_FLAG = '0'
            AND dt.TYPE = '1'
            AND dt.id IN (
                SELECT rel.TASK_ID
                FROM DPP_ETL_TASK_NODE_REL rel
                    JOIN DPP_ETL_NODE node
                        ON (node.CODE = rel.PRE_NODE_CODE OR node.CODE = rel.POST_NODE_CODE)
                WHERE
                <choose>
                    <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                        CAST(
                            SUBSTRING_INDEX(
                                SUBSTRING_INDEX(
                                    node.PARAMETERS,
                                    '"clmt":',
                                    -1
                                ),
                                ',',
                                1
                            ) AS UNSIGNED
                        ) = 1
                        AND
                            CAST(
                            SUBSTRING_INDEX(
                                SUBSTRING_INDEX(
                                    node.PARAMETERS,
                                    '"asset_id_cpoy":',
                                    -1
                                ),
                                ',',
                                1
                            ) AS UNSIGNED
                        )
                    </when>
                    <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                        TO_NUMBER(
                        REGEXP_SUBSTR(node.PARAMETERS,
                        '"clmt"[[:space:]]*:[[:space:]]*"?([0-9]+)"?',
                        1, 1, NULL, 1)
                        )=1 AND
                        TO_NUMBER(
                        REGEXP_SUBSTR(node.PARAMETERS,
                        '"asset_id_cpoy"[[:space:]]*:[[:space:]]*([0-9]+)',
                        1, 1, NULL, 1)
                        )
                    </when>
                </choose>
                           in
                          <foreach item="id" collection="assetIdList" open="(" separator="," close=")">
                              #{id}
                          </foreach>

            );
    </select>

    <select id="getDppEtlTaskPage" resultType="tech.qiantong.qdata.module.dpp.controller.admin.etl.vo.DppEtlTaskRespVO">
        SELECT
        t.*,
        t2.NAME AS catName,
        t3.CRON_EXPRESSION AS cronExpression,
        t3.STATUS AS schedulerState ,
        (SELECT MAX(ti.CREATE_TIME) FROM DPP_ETL_TASK_INSTANCE ti WHERE ti.TASK_CODE = t.CODE AND ti.DEL_FLAG = '0') AS lastExecuteTime
        <if test="params.type == '3'.toString()">
            <choose>
                <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                    ,JSON_VALUE(t.DRAFT_JSON, '$.typaCode') AS datasourceType
                </when>
                <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                    ,JSON_EXTRACT(t.DRAFT_JSON, '$.typaCode') AS datasourceType
                </when>
            </choose>
        </if>
        FROM
        DPP_ETL_TASK t
        LEFT JOIN
        <choose>
            <when test="params.type == '1'.toString()">
                ATT_TASK_CAT
            </when>
            <when test="params.type == '3'.toString()">
                ATT_DATA_DEV_CAT
            </when>
            <when test="params.type == '4'.toString()">
                ATT_JOB_CAT
            </when>
        </choose>
        t2 on t.CAT_CODE = t2.CODE AND t2.DEL_FLAG = '0'
        LEFT JOIN  DPP_ETL_SCHEDULER t3 ON t.id = t3.task_id AND t3.DEL_FLAG = '0'
        WHERE
        t.DEL_FLAG = '0'
        AND t.STATUS NOT IN ('-2', '-3')
        AND t.TYPE = #{params.type}
        <if test="params.catCode != null and params.catCode != ''">
            AND t.CAT_CODE LIKE CONCAT(#{params.catCode}, '%')
        </if>
        <if test="params.name != null and params.name != ''">
            AND t.NAME LIKE CONCAT('%',CONCAT(#{params.name},'%'))
        </if>
        <if test="params.code != null and params.code != ''">
            AND t.CODE = #{params.code}
        </if>
        <if test="params.projectId != null">
            AND t.PROJECT_ID = #{params.projectId}
        </if>
        <if test="params.projectCode != null and params.projectCode != ''">
            AND t.PROJECT_CODE = #{params.projectCode}
        </if>
        <if test="params.personCharge != null and params.personCharge != ''">
            AND t.PERSON_CHARGE = #{params.personCharge}
        </if>
        <if test="params.status != null and params.status != ''">
            AND t.STATUS = #{params.status}
        </if>
        <if test="params.type == '3'.toString() and params.processType != null and params.processType != ''">
            <choose>
                <when test="params.processType == '1'.toString()">
                    <choose>
                        <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                            AND JSON_VALUE(t.DRAFT_JSON, '$.typaCode') = 'FlinkStream'
                        </when>
                        <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                            AND JSON_EXTRACT(t.DRAFT_JSON, '$.typaCode') = 'FlinkStream'
                        </when>
                    </choose>
                </when>
                <when test="params.processType == '2'.toString()">
                    <choose>
                        <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                            AND JSON_VALUE(t.DRAFT_JSON, '$.typaCode') != 'FlinkStream'
                        </when>
                        <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                            AND JSON_EXTRACT(t.DRAFT_JSON, '$.typaCode') != 'FlinkStream'
                        </when>
                    </choose>
                </when>
            </choose>
        </if>
        <if test="params.type == '3'.toString() and params.datasourceType != null and params.datasourceType != ''">
            <choose>
                <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'dm8'">
                    AND JSON_VALUE(t.DRAFT_JSON, '$.typaCode') = #{params.datasourceType}
                </when>
                <when test="@tech.qiantong.qdata.mybatis.config.MasterDataSourceConfig@getDatabaseType() == 'mysql'">
                    AND JSON_EXTRACT(t.DRAFT_JSON, '$.typaCode') = #{params.datasourceType}
                </when>
            </choose>
        </if>
    </select>
</mapper>
