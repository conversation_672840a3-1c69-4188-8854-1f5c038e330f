package tech.qiantong.qdata.module.ds.controller.admin.api.vo;

import lombok.Data;
import tech.qiantong.qdata.module.ds.dal.dataobject.dto.ReqParam;
import tech.qiantong.qdata.module.ds.dal.dataobject.dto.ResParam;

import java.io.Serializable;
import java.util.List;

@Data
public class SqlParseVo implements Serializable {

    private static final long serialVersionUID=1L;

    private List<ReqParam> reqParams;
    private List<ResParam> resParams;
}
