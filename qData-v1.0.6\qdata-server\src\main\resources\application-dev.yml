# 主数据源选择
datasource:
  type: mysql

# Spring配置
spring:
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: admin
    password: Ej^iUNFLp9MQouc1
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 1
        max-concurrency: 10
  datasource:
    druid:
      stat-view-servlet:
        # 是否启用Druid的监控统计功能
        enabled: false
        # 访问Druid监控页面的用户名
        loginUsername: qdata
        # 访问Druid监控页面的密码
        loginPassword: 123456
    dynamic:
      druid:
        # 连接池初始化时创建的连接数量
        initial-size: 5
        # 连接池中最小空闲连接数
        min-idle: 5
        # 连接池中最大活动连接数
        maxActive: 20
        # 连接池等待连接的最长时间（毫秒）
        maxWait: 60000
        # 数据库连接超时时间（毫秒）
        connectTimeout: 30000
        # Socket超时时间（毫秒）
        socketTimeout: 60000
        # 空闲连接的检测周期（毫秒）
        timeBetweenEvictionRunsMillis: 60000
        # 最小空闲连接的存活时间（毫秒）
        minEvictableIdleTimeMillis: 300000
        # 用于检测连接是否有效的SQL语句
        validationQuery: SELECT 1 FROM DUAL
        # 是否在空闲时检测连接的有效性
        testWhileIdle: true
        # 借用连接时是否测试连接的有效性
        testOnBorrow: false
        # 归还连接时是否测试连接的有效性
        testOnReturn: false
        # 是否打开连接池的PreparedStatement缓存
        poolPreparedStatements: true
        # 每个连接池的PreparedStatement缓存上限
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置Druid的过滤器
        filters: stat,slf4j
        # Druid连接属性配置
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源配置
        master:
          # 动态加载的配置属性
          driver-class-name: ${${datasource.type}.driver-class-name}
          url: ${${datasource.type}.url}
          username: ${${datasource.type}.username}
          password: ${${datasource.type}.password}
#        test:
#          driver-class-name: dm.jdbc.driver.DmDriver
#          url: jdbc:dm://127.0.0.1:5236/MOON?STU&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&schema=MOON&serverTimezone=Asia/Shanghai
#          username: MOON
#          password: 1234567890

# MySQL配置文件
mysql:
  # JDBC驱动类名
  driver-class-name: com.mysql.cj.jdbc.Driver
  # 主库JDBC连接URL
  url: ***************************************************************************************************************************************
  # 主库用户名
  username: root
  # 主库密码
  password: wangming1114

# 达梦配置文件
dm8:
  # JDBC驱动类名
  driver-class-name: dm.jdbc.driver.DmDriver
  # 主库JDBC连接URL
  url: jdbc:dm://127.0.0.1:5236/QDATA?STU&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&schema=QDATA&serverTimezone=Asia/Shanghai
  # 主库用户名
  username: QDATA
  # 主库密码
  password: 密码

# 人大金仓配置文件
kingbase8:
  # JDBC驱动类名
  driver-class-name: com.kingbase8.Driver
  # 主库JDBC连接URL
  url: ***********************************************************************************************************************************************************************************************
  # 主库用户名
  username: kingbase
  # 主库密码
  password: 123456

# Oracle12c 配置文件
oracle:
  # JDBC驱动类名
  driver-class-name: oracle.jdbc.OracleDriver
  # 主库JDBC连接URL
  url: ************************************************************************************************************************************
  # 主库用户名
  username: ANIVIA
  # 主库密码
  password: ANIVIA

# 调度器相关配置
ds:
  # 调度器api前缀
  base_url: http://127.0.0.1:12345/dolphinscheduler
  # 数据质量服务组件HTTP地址
  quality_url: http://127.0.0.1:8083/quality/qualityTaskExecutor/runExecuteTask
  # 数据质量任务dolphinscheduler项目编码
  http_quality_projectCode: 134799536571008
  # 调度器令牌（可以自行修改）
  token: b07f0e57c4818043a57ba05a28da291a
  # 资源中心路径
  resource_url: /dolphinscheduler/
  # spark相关配置
  spark:
    #spark-master的地址
    master_url: spark://127.0.0.1:7077
    #jar包在ds的路径
    main_jar: file:/dolphinscheduler/default/resources/spark-jar/qdata-etl-3.8.8.jar
    #jar main入口
    main_class: tech.qiantong.qdata.spark.etl.EtlApplication
  # hdfs相关配置
  hdfs:
    #NameNode RPC地址（客户端连接 HDFS 的入口地址）
    url: hdfs://127.0.0.1:8020
  # 调度器 Redis 配置（用于获取最新数据源信息，保障任务执行）
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:J98%FHF#9h@e88h9fre9

path:
  # 数据质量服务前缀
  quality_url: http://127.0.0.1:8083/quality/qualityTaskExecutor
